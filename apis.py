from fastapi import FastAP<PERSON>, UploadFile, File
import pandas as pd
import use_case1
import use_case2
import tempfile
import os

app = FastAPI(
    title="BOM Automation API",
    description="API for BOM Use Case 1 (Part Standardization) and Use Case 2 (BOM Comparison & Department Summaries)",
    version="1.0"
)

# ---------------- Use Case 1: Part Number Standardization ----------------
@app.post("/standardize_parts/")
async def standardize_parts(bom_file: UploadFile = File(...)):
    try:
        # Save uploaded file temporarily
        with tempfile.NamedTemporaryFile(delete=False, suffix=".xlsx") as tmp:
            tmp.write(await bom_file.read())
            tmp_path = tmp.name

        # Run Use Case 1 pipeline
        data = use_case1.load_parts_from_excel(tmp_path)
        data = use_case1.preprocess_parts(data)
        duplicates = use_case1.detect_duplicates(data)
        result_df = use_case1.assign_master_part_number(data, duplicates)

        os.remove(tmp_path)  # cleanup temp file
        return {
            "total_parts": len(result_df),
            "unique_master_parts": result_df["master_part_number"].nunique(),
            "duplicates_found": len(duplicates),
            "result": result_df.to_dict(orient="records")
        }
    except Exception as e:
        return {"error": str(e)}

# ---------------- Use Case 2: BOM Comparison + Department Summaries ----------------
@app.post("/compare_boms/")
async def compare_boms(old_bom: UploadFile = File(...), new_bom: UploadFile = File(...)):
    try:
        # Save uploaded files temporarily
        with tempfile.NamedTemporaryFile(delete=False, suffix=".xlsx") as tmp1:
            tmp1.write(await old_bom.read())
            old_path = tmp1.name
        with tempfile.NamedTemporaryFile(delete=False, suffix=".xlsx") as tmp2:
            tmp2.write(await new_bom.read())
            new_path = tmp2.name

        # Run Use Case 2 pipeline
        old_bom_data = use_case2.load_bom(old_path)
        new_bom_data = use_case2.load_bom(new_path)
        changes = use_case2.detect_bom_changes(old_bom_data, new_bom_data)

        # Generate department-wise summaries (without email sending for API response)
        department_summaries = {}
        for dept in ["Procurement", "Production", "QA"]:
            department_summaries[dept] = use_case2.generate_summary(changes, dept)

        # Cleanup
        os.remove(old_path)
        os.remove(new_path)

        return {
            "differences": changes,
            "summaries": department_summaries
        }
    except Exception as e:
        return {"error": str(e)}
