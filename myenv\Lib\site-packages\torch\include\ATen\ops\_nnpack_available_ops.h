#pragma once

// @generated by torchgen/gen.py from Operator.h

#include <string_view>
#include <tuple>
#include <vector>

// Forward declarations of any types needed in the operator signatures.
// We can't directly include these classes because it will cause circular include dependencies.
// This file is included by TensorBody.h, which defines the Tensor class.
#include <ATen/core/ATen_fwd.h>

namespace at {
namespace _ops {


struct TORCH_API _nnpack_available {
  using schema = bool ();
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::_nnpack_available";
  static constexpr const char* overload_name = "";
  static constexpr const char* schema_str = "_nnpack_available() -> bool";
  static bool call();
  static bool redispatch(c10::DispatchKeySet dispatchKeySet);
};

}} // namespace at::_ops
