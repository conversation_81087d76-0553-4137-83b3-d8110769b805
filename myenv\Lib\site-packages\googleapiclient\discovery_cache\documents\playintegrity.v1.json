{"auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/playintegrity": {"description": "Private Service: https://www.googleapis.com/auth/playintegrity"}}}}, "basePath": "", "baseUrl": "https://playintegrity.googleapis.com/", "batchPath": "batch", "canonicalName": "Play Integrity", "description": "The Play Integrity API helps you check that you're interacting with your genuine app on a genuine Android device powered by Google Play services. The Play Integrity API has replaced SafetyNet Attestation and Android Device Verification.", "discoveryVersion": "v1", "documentationLink": "https://developer.android.com/google/play/integrity", "fullyEncodeReservedExpansion": true, "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "playintegrity:v1", "kind": "discovery#restDescription", "mtlsRootUrl": "https://playintegrity.mtls.googleapis.com/", "name": "playintegrity", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"deviceRecall": {"methods": {"write": {"description": "Writes recall bits for the device where Play Integrity API token is obtained. The endpoint is available to select Play partners in an early access program (EAP).", "flatPath": "v1/{v1Id}/deviceRecall:write", "httpMethod": "POST", "id": "playintegrity.deviceRecall.write", "parameterOrder": ["packageName"], "parameters": {"packageName": {"description": "Required. Package name of the app the attached integrity token belongs to.", "location": "path", "pattern": "^[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+packageName}/deviceRecall:write", "request": {"$ref": "WriteDeviceRecallRequest"}, "response": {"$ref": "WriteDeviceRecallResponse"}, "scopes": ["https://www.googleapis.com/auth/playintegrity"]}}}, "v1": {"methods": {"decodeIntegrityToken": {"description": "Decodes the integrity token and returns the token payload.", "flatPath": "v1/{v1Id}:decodeIntegrityToken", "httpMethod": "POST", "id": "playintegrity.decodeIntegrityToken", "parameterOrder": ["packageName"], "parameters": {"packageName": {"description": " Package name of the app the attached integrity token belongs to.", "location": "path", "pattern": "^[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+packageName}:decodeIntegrityToken", "request": {"$ref": "DecodeIntegrityTokenRequest"}, "response": {"$ref": "DecodeIntegrityTokenResponse"}, "scopes": ["https://www.googleapis.com/auth/playintegrity"]}, "decodePcIntegrityToken": {"description": "Decodes the PC integrity token and returns the PC token payload.", "flatPath": "v1/{v1Id}:decodePcIntegrityToken", "httpMethod": "POST", "id": "playintegrity.decodePcIntegrityToken", "parameterOrder": ["packageName"], "parameters": {"packageName": {"description": "Package name of the app the attached integrity token belongs to.", "location": "path", "pattern": "^[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+packageName}:decodePcIntegrityToken", "request": {"$ref": "DecodePcIntegrityTokenRequest"}, "response": {"$ref": "DecodePcIntegrityTokenResponse"}, "scopes": ["https://www.googleapis.com/auth/playintegrity"]}}}}, "revision": "********", "rootUrl": "https://playintegrity.googleapis.com/", "schemas": {"AccountActivity": {"deprecated": true, "description": "(Restricted Access) Contains a signal helping apps differentiating between likely genuine and likely non-genuine user traffic.", "id": "AccountActivity", "properties": {"activityLevel": {"description": "Required. Indicates the activity level of the account.", "enum": ["ACTIVITY_LEVEL_UNSPECIFIED", "UNEVALUATED", "UNUSUAL", "UNKNOWN", "TYPICAL_BASIC", "TYPICAL_STRONG"], "enumDescriptions": ["Activity level has not been set.", "Account activity level is not evaluated.", "Unusual activity for at least one of the user accounts on the device.", "Insufficient activity to verify the user account on the device.", "Typical activity for the user account or accounts on the device.", "Typical for the user account or accounts on the device, with harder to replicate signals."], "type": "string"}}, "type": "object"}, "AccountDetails": {"description": "Contains the account information such as the licensing status for the user in the scope.", "id": "AccountDetails", "properties": {"accountActivity": {"$ref": "AccountActivity", "deprecated": true, "description": "(Restricted Access) Details about the account activity for the user in the scope."}, "appLicensingVerdict": {"description": "Required. Details about the licensing status of the user for the app in the scope.", "enum": ["UNKNOWN", "LICENSED", "UNLICENSED", "UNEVALUATED"], "enumDescriptions": ["Play does not have sufficient information to evaluate licensing details", "The app and certificate match the versions distributed by Play.", "The certificate or package name does not match Google Play records.", "Licensing details were not evaluated since a necessary requirement was missed. For example DeviceIntegrity did not meet the minimum bar or the application was not a known Play version."], "type": "string"}}, "type": "object"}, "AppAccessRiskVerdict": {"description": "Contains signals about others apps on the device which could be used to access or control the requesting app.", "id": "AppAccessRiskVerdict", "properties": {"appsDetected": {"description": "List of detected app types signalled for App Access Risk.", "items": {"enum": ["APPS_DETECTED_UNSPECIFIED", "KNOWN_INSTALLED", "KNOWN_CAPTURING", "KNOWN_OVERLAYS", "KNOWN_CONTROLLING", "UNKNOWN_INSTALLED", "UNKNOWN_CAPTURING", "UNKNOWN_OVERLAYS", "UNKNOWN_CONTROLLING"], "enumDescriptions": ["Apps detected is unspecified.", "One or more apps is installed by Google Play or preloaded on the system partition by the device manufacturer.", "One or more apps installed by Google Play or preloaded on the device is running that could be used to read or capture the requesting app, such as a screen recording app.", "One or more apps installed by Google Play or preloaded on the device is running that could be used to display overlays over the requesting app.", "One or more apps installed by Google Play or preloaded on the device is running that could be used to control the device, such as a remote support app.", "One or more unknown apps is installed, that were not installed by Google Play or preloaded on the system partition by the device manufacturer.", "One or more unknown apps, which were not installed by Google Play or preloaded on the device, is running that could be used to read or capture the requesting app, such as a screen recording app.", "One or more unknown apps, which were not installed by Google Play or preloaded on the device, is running that could be used to display overlays over the requesting app.", "One or more unknown apps, which were not installed by Google Play or preloaded on the device, is running that could be used to control the device, such as a remote support app."], "type": "string"}, "type": "array"}}, "type": "object"}, "AppIntegrity": {"description": "Contains the application integrity information.", "id": "AppIntegrity", "properties": {"appRecognitionVerdict": {"description": "Required. Details about the app recognition verdict", "enum": ["UNKNOWN", "PLAY_RECOGNIZED", "UNRECOGNIZED_VERSION", "UNEVALUATED"], "enumDescriptions": ["Play does not have sufficient information to evaluate app integrity", "The app and certificate match the versions distributed by Play.", "The certificate or package name does not match Google Play records.", "Application integrity was not evaluated since a necessary requirement was missed. For example DeviceIntegrity did not meet the minimum bar."], "type": "string"}, "certificateSha256Digest": {"description": "The SHA256 hash of the requesting app's signing certificates (base64 web-safe encoded). Set iff app_recognition_verdict != UNEVALUATED.", "items": {"type": "string"}, "type": "array"}, "packageName": {"description": "Package name of the application under attestation. Set iff app_recognition_verdict != UNEVALUATED.", "type": "string"}, "versionCode": {"description": "Version code of the application. Set iff app_recognition_verdict != UNEVALUATED.", "format": "int64", "type": "string"}}, "type": "object"}, "DecodeIntegrityTokenRequest": {"description": "Request to decode the integrity token.", "id": "DecodeIntegrityTokenRequest", "properties": {"integrityToken": {"description": "Encoded integrity token.", "type": "string"}}, "type": "object"}, "DecodeIntegrityTokenResponse": {"description": "Response containing the decoded integrity payload.", "id": "DecodeIntegrityTokenResponse", "properties": {"tokenPayloadExternal": {"$ref": "TokenPayloadExternal", "description": "Plain token payload generated from the decoded integrity token."}}, "type": "object"}, "DecodePcIntegrityTokenRequest": {"description": "Request to decode the PC integrity token.", "id": "DecodePcIntegrityTokenRequest", "properties": {"integrityToken": {"description": "Encoded integrity token.", "type": "string"}}, "type": "object"}, "DecodePcIntegrityTokenResponse": {"description": "Response containing the decoded PC integrity payload.", "id": "DecodePcIntegrityTokenResponse", "properties": {"tokenPayloadExternal": {"$ref": "PcTokenPayloadExternal", "description": "Plain token payload generated from the decoded integrity token."}}, "type": "object"}, "DeviceAttributes": {"description": "Contains information about the device for which the integrity token was generated, e.g. Android SDK version.", "id": "DeviceAttributes", "properties": {"sdkVersion": {"description": "Android SDK version of the device, as defined in the public Android documentation: https://developer.android.com/reference/android/os/Build.VERSION_CODES. It won't be set if a necessary requirement was missed. For example DeviceIntegrity did not meet the minimum bar.", "format": "int32", "type": "integer"}}, "type": "object"}, "DeviceIntegrity": {"description": "Contains the device attestation information.", "id": "DeviceIntegrity", "properties": {"deviceAttributes": {"$ref": "DeviceAttributes", "description": "Attributes of the device where the integrity token was generated."}, "deviceRecall": {"$ref": "DeviceRecall", "description": "Details about the device recall bits set by the developer."}, "deviceRecognitionVerdict": {"description": "Details about the integrity of the device the app is running on.", "items": {"enum": ["UNKNOWN", "MEETS_BASIC_INTEGRITY", "MEETS_DEVICE_INTEGRITY", "MEETS_STRONG_INTEGRITY", "MEETS_VIRTUAL_INTEGRITY"], "enumDescriptions": ["Play does not have sufficient information to evaluate device integrity", "App is running on a device that passes basic system integrity checks, but may not meet Android platform compatibility requirements and may not be approved to run Google Play services.", "App is running on GMS Android device with Google Play services.", "App is running on GMS Android device with Google Play services and has a strong guarantee of system integrity such as a hardware-backed keystore.", "App is running on an Android emulator with Google Play services which meets core Android compatibility requirements."], "type": "string"}, "type": "array"}, "legacyDeviceRecognitionVerdict": {"description": "Contains legacy details about the integrity of the device the app is running on. Only for devices with Android version T or higher and only for apps opted in to the new verdicts. Only available during the transition period to the new verdicts system and will be removed afterwards.", "items": {"enum": ["UNKNOWN", "MEETS_BASIC_INTEGRITY", "MEETS_DEVICE_INTEGRITY", "MEETS_STRONG_INTEGRITY", "MEETS_VIRTUAL_INTEGRITY"], "enumDescriptions": ["Play does not have sufficient information to evaluate device integrity", "App is running on a device that passes basic system integrity checks, but may not meet Android platform compatibility requirements and may not be approved to run Google Play services.", "App is running on GMS Android device with Google Play services.", "App is running on GMS Android device with Google Play services and has a strong guarantee of system integrity such as a hardware-backed keystore.", "App is running on an Android emulator with Google Play services which meets core Android compatibility requirements."], "type": "string"}, "type": "array"}, "recentDeviceActivity": {"$ref": "RecentDeviceActivity", "description": "Details about the device activity of the device the app is running on."}}, "type": "object"}, "DeviceRecall": {"description": "Contains the recall bits per device set by the developer.", "id": "DeviceRecall", "properties": {"values": {"$ref": "Values", "description": "Required. Contains the recall bits values."}, "writeDates": {"$ref": "WriteDates", "description": "Required. Contains the recall bits write dates."}}, "type": "object"}, "EnvironmentDetails": {"description": "Contains information about the environment Play Integrity API runs in, e.g. Play Protect verdict.", "id": "EnvironmentDetails", "properties": {"appAccessRiskVerdict": {"$ref": "AppAccessRiskVerdict", "description": "The evaluation of the App Access Risk verdicts."}, "playProtectVerdict": {"description": "The evaluation of Play Protect verdict.", "enum": ["PLAY_PROTECT_VERDICT_UNSPECIFIED", "UNEVALUATED", "NO_ISSUES", "NO_DATA", "MEDIUM_RISK", "HIGH_RISK", "POSSIBLE_RISK"], "enumDescriptions": ["Play Protect verdict has not been set.", "Play Protect state was not evaluated. <PERSON><PERSON> may not be trusted.", "Play Protect is on and no issues found.", "Play Protect is on but no scan has been performed yet. The device or Play Store app may have been reset.", "Play Protect is on and warnings found.", "Play Protect is on and high severity issues found.", "Play Protect is turned off. Turn on Play Protect."], "type": "string"}}, "type": "object"}, "PcDeviceIntegrity": {"description": "Contains the device attestation information.", "id": "PcDeviceIntegrity", "properties": {"deviceRecognitionVerdict": {"description": "Details about the integrity of the device the app is running on.", "items": {"enum": ["DEVICE_RECOGNITION_VERDICT_UNSPECIFIED", "MEETS_PC_INTEGRITY"], "enumDescriptions": ["Unspecified device integrity.", "App is running on Windows Device with Google Desktop Services."], "type": "string"}, "type": "array"}}, "type": "object"}, "PcRequestDetails": {"description": "Contains the integrity request information.", "id": "PcRequestDetails", "properties": {"requestHash": {"description": "Request hash that was provided in the request.", "type": "string"}, "requestPackageName": {"description": "Required. Application package name this attestation was requested for. Note: This field makes no guarantees or promises on the caller integrity.", "type": "string"}, "requestTime": {"description": "Required. Timestamp, of the integrity application request.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "PcTokenPayloadExternal": {"description": "Contains PC device attestation details.", "id": "PcTokenPayloadExternal", "properties": {"deviceIntegrity": {"$ref": "PcDeviceIntegrity", "description": "Required. Details about the device integrity."}, "requestDetails": {"$ref": "PcRequestDetails", "description": "Required. Details about the integrity request."}}, "type": "object"}, "RecentDeviceActivity": {"description": "Recent device activity can help developers identify devices that have exhibited hyperactive attestation activity, which could be a sign of an attack or token farming.", "id": "RecentDeviceActivity", "properties": {"deviceActivityLevel": {"description": "Required. Indicates the activity level of the device.", "enum": ["DEVICE_ACTIVITY_LEVEL_UNSPECIFIED", "UNEVALUATED", "LEVEL_1", "LEVEL_2", "LEVEL_3", "LEVEL_4"], "enumDescriptions": ["Device activity level has not been set.", "Device activity level has not been evaluated.", "Indicates the amount of used tokens. See the documentation for details.", "Indicates the amount of used tokens. See the documentation for details.", "Indicates the amount of used tokens. See the documentation for details.", "Indicates the amount of used tokens. See the documentation for details."], "type": "string"}}, "type": "object"}, "RequestDetails": {"description": "Contains the integrity request information.", "id": "RequestDetails", "properties": {"nonce": {"description": "Nonce that was provided in the request (which is base64 web-safe no-wrap).", "type": "string"}, "requestHash": {"description": "Request hash that was provided in the request.", "type": "string"}, "requestPackageName": {"description": "Required. Application package name this attestation was requested for. Note: This field makes no guarantees or promises on the caller integrity. For details on application integrity, check application_integrity.", "type": "string"}, "timestampMillis": {"description": "Required. Timestamp, in milliseconds, of the integrity application request.", "format": "int64", "type": "string"}}, "type": "object"}, "TestingDetails": {"description": "Contains additional information generated for testing responses.", "id": "TestingDetails", "properties": {"isTestingResponse": {"description": "Required. Indicates that the information contained in this payload is a testing response that is statically overridden for a tester.", "type": "boolean"}}, "type": "object"}, "TokenPayloadExternal": {"description": "Contains basic app information and integrity signals like device attestation and licensing details.", "id": "TokenPayloadExternal", "properties": {"accountDetails": {"$ref": "AccountDetails", "description": "Required. Details about the Play Store account."}, "appIntegrity": {"$ref": "AppIntegrity", "description": "Required. Details about the application integrity."}, "deviceIntegrity": {"$ref": "DeviceIntegrity", "description": "Required. Details about the device integrity."}, "environmentDetails": {"$ref": "EnvironmentDetails", "description": "Details of the environment Play Integrity API runs in."}, "requestDetails": {"$ref": "RequestDetails", "description": "Required. Details about the integrity request."}, "testingDetails": {"$ref": "TestingDetails", "description": "Indicates that this payload is generated for testing purposes and contains any additional data that is linked with testing status."}}, "type": "object"}, "Values": {"description": "Contains the recall bits values.", "id": "Values", "properties": {"bitFirst": {"description": "Required. First recall bit value.", "type": "boolean"}, "bitSecond": {"description": "Required. Second recall bit value.", "type": "boolean"}, "bitThird": {"description": "Required. Third recall bit value.", "type": "boolean"}}, "type": "object"}, "WriteDates": {"description": "Contains the recall bits write dates.", "id": "WriteDates", "properties": {"yyyymmFirst": {"description": "Optional. Write time in YYYYMM format (in UTC, e.g. 202402) for the first bit. Note that this value won't be set if the first bit is false.", "format": "int32", "type": "integer"}, "yyyymmSecond": {"description": "Optional. Write time in YYYYMM format (in UTC, e.g. 202402) for the second bit. Note that this value won't be set if the second bit is false.", "format": "int32", "type": "integer"}, "yyyymmThird": {"description": "Optional. Write time in YYYYMM format (in UTC, e.g. 202402) for the third bit. Note that this value won't be set if the third bit is false.", "format": "int32", "type": "integer"}}, "type": "object"}, "WriteDeviceRecallRequest": {"description": "Request to write device recall bits.", "id": "WriteDeviceRecallRequest", "properties": {"integrityToken": {"description": "Required. Integrity token obtained from calling Play Integrity API.", "type": "string"}, "newValues": {"$ref": "Values", "description": "Required. The new values for the device recall bits to be written."}}, "type": "object"}, "WriteDeviceRecallResponse": {"description": "Response for the Write Device Recall action. Currently empty.", "id": "WriteDeviceRecallResponse", "properties": {}, "type": "object"}}, "servicePath": "", "title": "Google Play Integrity API", "version": "v1", "version_module": true}