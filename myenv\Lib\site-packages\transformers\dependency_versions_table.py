# THIS FILE HAS BEEN AUTOGENERATED. To update:
# 1. modify the `_deps` dict in setup.py
# 2. run `make deps_table_update``
deps = {
    "Pillow": "Pillow>=10.0.1,<=15.0",
    "accelerate": "accelerate>=0.26.0",
    "av": "av",
    "beautifulsoup4": "beautifulsoup4",
    "blobfile": "blobfile",
    "codecarbon": "codecarbon>=2.8.1",
    "cookiecutter": "cookiecutter==1.7.3",
    "dataclasses": "dataclasses",
    "datasets": "datasets>=2.15.0",
    "deepspeed": "deepspeed>=0.9.3",
    "diffusers": "diffusers",
    "dill": "dill<0.3.5",
    "evaluate": "evaluate>=0.2.0",
    "faiss-cpu": "faiss-cpu",
    "fastapi": "fastapi",
    "filelock": "filelock",
    "flax": "flax>=0.4.1,<=0.7.0",
    "ftfy": "ftfy",
    "fugashi": "fugashi>=1.0",
    "GitPython": "GitPython<3.1.19",
    "hf-doc-builder": "hf-doc-builder>=0.3.0",
    "hf_xet": "hf_xet",
    "huggingface-hub": "huggingface-hub>=0.34.0,<1.0",
    "importlib_metadata": "importlib_metadata",
    "ipadic": "ipadic>=1.0.0,<2.0",
    "jax": "jax>=0.4.1,<=0.4.13",
    "jaxlib": "jaxlib>=0.4.1,<=0.4.13",
    "jieba": "jieba",
    "jinja2": "jinja2>=3.1.0",
    "kenlm": "kenlm",
    "keras": "keras>2.9,<2.16",
    "keras-nlp": "keras-nlp>=0.3.1,<0.14.0",
    "kernels": "kernels>=0.6.1,<=0.9",
    "librosa": "librosa",
    "natten": "natten>=0.14.6,<0.15.0",
    "nltk": "nltk<=3.8.1",
    "num2words": "num2words",
    "numpy": "numpy>=1.17",
    "onnxconverter-common": "onnxconverter-common",
    "onnxruntime-tools": "onnxruntime-tools>=1.4.2",
    "onnxruntime": "onnxruntime>=1.4.0",
    "openai": "openai>=1.98.0",
    "opencv-python": "opencv-python",
    "optimum-benchmark": "optimum-benchmark>=0.3.0",
    "optuna": "optuna",
    "optax": "optax>=0.0.8,<=0.1.4",
    "pandas": "pandas<2.3.0",
    "packaging": "packaging>=20.0",
    "parameterized": "parameterized>=0.9",
    "phonemizer": "phonemizer",
    "protobuf": "protobuf",
    "psutil": "psutil",
    "pyyaml": "pyyaml>=5.1",
    "pydantic": "pydantic>=2",
    "pytest": "pytest>=7.2.0",
    "pytest-asyncio": "pytest-asyncio",
    "pytest-rerunfailures": "pytest-rerunfailures",
    "pytest-timeout": "pytest-timeout",
    "pytest-xdist": "pytest-xdist",
    "pytest-order": "pytest-order",
    "python": "python>=3.9.0",
    "ray[tune]": "ray[tune]>=2.7.0",
    "regex": "regex!=2019.12.17",
    "requests": "requests",
    "rhoknp": "rhoknp>=1.1.0,<1.3.1",
    "rjieba": "rjieba",
    "rouge-score": "rouge-score!=0.0.7,!=0.0.8,!=0.1,!=0.1.1",
    "ruff": "ruff==0.11.2",
    "sacrebleu": "sacrebleu>=1.4.12,<2.0.0",
    "sacremoses": "sacremoses",
    "safetensors": "safetensors>=0.4.3",
    "sagemaker": "sagemaker>=2.31.0",
    "schedulefree": "schedulefree>=1.2.6",
    "scikit-learn": "scikit-learn",
    "scipy": "scipy<1.13.0",
    "sentencepiece": "sentencepiece>=0.1.91,!=0.1.92",
    "sigopt": "sigopt",
    "starlette": "starlette",
    "sudachipy": "sudachipy>=0.6.6",
    "sudachidict_core": "sudachidict_core>=20220729",
    "tensorboard": "tensorboard",
    "tensorflow-cpu": "tensorflow-cpu>2.9,<2.16",
    "tensorflow": "tensorflow>2.9,<2.16",
    "tensorflow-text": "tensorflow-text<2.16",
    "tensorflow-probability": "tensorflow-probability<0.24",
    "tf2onnx": "tf2onnx",
    "timeout-decorator": "timeout-decorator",
    "tiktoken": "tiktoken",
    "timm": "timm<=1.0.19,!=1.0.18",
    "tokenizers": "tokenizers>=0.21,<0.22",
    "torch": "torch>=2.1",
    "torchaudio": "torchaudio",
    "torchvision": "torchvision",
    "pyctcdecode": "pyctcdecode>=0.4.0",
    "tqdm": "tqdm>=4.27",
    "unidic": "unidic>=1.0.2",
    "unidic_lite": "unidic_lite>=1.0.7",
    "urllib3": "urllib3<2.0.0",
    "uvicorn": "uvicorn",
    "pytest-rich": "pytest-rich",
    "libcst": "libcst",
    "rich": "rich",
    "opentelemetry-api": "opentelemetry-api",
    "opentelemetry-exporter-otlp": "opentelemetry-exporter-otlp",
    "opentelemetry-sdk": "opentelemetry-sdk",
    "mistral-common[opencv]": "mistral-common[opencv]>=1.6.3",
}
