{"auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/cloud-language": {"description": "Apply machine learning models to reveal the structure and meaning of text"}, "https://www.googleapis.com/auth/cloud-platform": {"description": "See, edit, configure, and delete your Google Cloud data and see the email address for your Google Account."}}}}, "basePath": "", "baseUrl": "https://language.googleapis.com/", "batchPath": "batch", "canonicalName": "Cloud Natural Language", "description": "Provides natural language understanding technologies, such as sentiment analysis, entity recognition, entity sentiment analysis, and other text annotations, to developers.", "discoveryVersion": "v1", "documentationLink": "https://cloud.google.com/natural-language/", "fullyEncodeReservedExpansion": true, "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "language:v1beta1", "kind": "discovery#restDescription", "mtlsRootUrl": "https://language.mtls.googleapis.com/", "name": "language", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"documents": {"methods": {"analyzeEntities": {"description": "Finds named entities (currently proper names and common nouns) in the text along with entity types, salience, mentions for each entity, and other properties.", "flatPath": "v1beta1/documents:analyzeEntities", "httpMethod": "POST", "id": "language.documents.analyzeEntities", "parameterOrder": [], "parameters": {}, "path": "v1beta1/documents:analyzeEntities", "request": {"$ref": "AnalyzeEntitiesRequest"}, "response": {"$ref": "AnalyzeEntitiesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-language", "https://www.googleapis.com/auth/cloud-platform"]}, "analyzeSentiment": {"description": "Analyzes the sentiment of the provided text.", "flatPath": "v1beta1/documents:analyzeSentiment", "httpMethod": "POST", "id": "language.documents.analyzeSentiment", "parameterOrder": [], "parameters": {}, "path": "v1beta1/documents:analyzeSentiment", "request": {"$ref": "AnalyzeSentimentRequest"}, "response": {"$ref": "AnalyzeSentimentResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-language", "https://www.googleapis.com/auth/cloud-platform"]}, "analyzeSyntax": {"description": "Analyzes the syntax of the text and provides sentence boundaries and tokenization along with part of speech tags, dependency trees, and other properties.", "flatPath": "v1beta1/documents:analyzeSyntax", "httpMethod": "POST", "id": "language.documents.analyzeSyntax", "parameterOrder": [], "parameters": {}, "path": "v1beta1/documents:analyzeSyntax", "request": {"$ref": "AnalyzeSyntaxRequest"}, "response": {"$ref": "AnalyzeSyntaxResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-language", "https://www.googleapis.com/auth/cloud-platform"]}, "annotateText": {"description": "A convenience method that provides all the features that analyzeSentiment, analyzeEntities, and analyzeSyntax provide in one call.", "flatPath": "v1beta1/documents:annotateText", "httpMethod": "POST", "id": "language.documents.annotateText", "parameterOrder": [], "parameters": {}, "path": "v1beta1/documents:annotateText", "request": {"$ref": "AnnotateTextRequest"}, "response": {"$ref": "AnnotateTextResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-language", "https://www.googleapis.com/auth/cloud-platform"]}}}}, "revision": "20230104", "rootUrl": "https://language.googleapis.com/", "schemas": {"AnalyzeEntitiesRequest": {"description": "The entity analysis request message.", "id": "AnalyzeEntitiesRequest", "properties": {"document": {"$ref": "Document", "description": "Input document."}, "encodingType": {"description": "The encoding type used by the API to calculate offsets.", "enum": ["NONE", "UTF8", "UTF16", "UTF32"], "enumDescriptions": ["If `EncodingType` is not specified, encoding-dependent information (such as `begin_offset`) will be set at `-1`.", "Encoding-dependent information (such as `begin_offset`) is calculated based on the UTF-8 encoding of the input. C++ and Go are examples of languages that use this encoding natively.", "Encoding-dependent information (such as `begin_offset`) is calculated based on the UTF-16 encoding of the input. Java and Javascript are examples of languages that use this encoding natively.", "Encoding-dependent information (such as `begin_offset`) is calculated based on the UTF-32 encoding of the input. Python is an example of a language that uses this encoding natively."], "type": "string"}}, "type": "object"}, "AnalyzeEntitiesResponse": {"description": "The entity analysis response message.", "id": "AnalyzeEntitiesResponse", "properties": {"entities": {"description": "The recognized entities in the input document.", "items": {"$ref": "Entity"}, "type": "array"}, "language": {"description": "The language of the text, which will be the same as the language specified in the request or, if not specified, the automatically-detected language. See Document.language field for more details.", "type": "string"}}, "type": "object"}, "AnalyzeSentimentRequest": {"description": "The sentiment analysis request message.", "id": "AnalyzeSentimentRequest", "properties": {"document": {"$ref": "Document", "description": "Input document."}, "encodingType": {"description": "The encoding type used by the API to calculate sentence offsets for the sentence sentiment.", "enum": ["NONE", "UTF8", "UTF16", "UTF32"], "enumDescriptions": ["If `EncodingType` is not specified, encoding-dependent information (such as `begin_offset`) will be set at `-1`.", "Encoding-dependent information (such as `begin_offset`) is calculated based on the UTF-8 encoding of the input. C++ and Go are examples of languages that use this encoding natively.", "Encoding-dependent information (such as `begin_offset`) is calculated based on the UTF-16 encoding of the input. Java and Javascript are examples of languages that use this encoding natively.", "Encoding-dependent information (such as `begin_offset`) is calculated based on the UTF-32 encoding of the input. Python is an example of a language that uses this encoding natively."], "type": "string"}}, "type": "object"}, "AnalyzeSentimentResponse": {"description": "The sentiment analysis response message.", "id": "AnalyzeSentimentResponse", "properties": {"documentSentiment": {"$ref": "Sentiment", "description": "The overall sentiment of the input document."}, "language": {"description": "The language of the text, which will be the same as the language specified in the request or, if not specified, the automatically-detected language. See Document.language field for more details.", "type": "string"}, "sentences": {"description": "The sentiment for all the sentences in the document.", "items": {"$ref": "Sentence"}, "type": "array"}}, "type": "object"}, "AnalyzeSyntaxRequest": {"description": "The syntax analysis request message.", "id": "AnalyzeSyntaxRequest", "properties": {"document": {"$ref": "Document", "description": "Input document."}, "encodingType": {"description": "The encoding type used by the API to calculate offsets.", "enum": ["NONE", "UTF8", "UTF16", "UTF32"], "enumDescriptions": ["If `EncodingType` is not specified, encoding-dependent information (such as `begin_offset`) will be set at `-1`.", "Encoding-dependent information (such as `begin_offset`) is calculated based on the UTF-8 encoding of the input. C++ and Go are examples of languages that use this encoding natively.", "Encoding-dependent information (such as `begin_offset`) is calculated based on the UTF-16 encoding of the input. Java and Javascript are examples of languages that use this encoding natively.", "Encoding-dependent information (such as `begin_offset`) is calculated based on the UTF-32 encoding of the input. Python is an example of a language that uses this encoding natively."], "type": "string"}}, "type": "object"}, "AnalyzeSyntaxResponse": {"description": "The syntax analysis response message.", "id": "AnalyzeSyntaxResponse", "properties": {"language": {"description": "The language of the text, which will be the same as the language specified in the request or, if not specified, the automatically-detected language. See Document.language field for more details.", "type": "string"}, "sentences": {"description": "Sentences in the input document.", "items": {"$ref": "Sentence"}, "type": "array"}, "tokens": {"description": "Tokens, along with their syntactic information, in the input document.", "items": {"$ref": "Token"}, "type": "array"}}, "type": "object"}, "AnnotateTextRequest": {"description": "The request message for the text annotation API, which can perform multiple analysis types (sentiment, entities, and syntax) in one call.", "id": "AnnotateTextRequest", "properties": {"document": {"$ref": "Document", "description": "Input document."}, "encodingType": {"description": "The encoding type used by the API to calculate offsets.", "enum": ["NONE", "UTF8", "UTF16", "UTF32"], "enumDescriptions": ["If `EncodingType` is not specified, encoding-dependent information (such as `begin_offset`) will be set at `-1`.", "Encoding-dependent information (such as `begin_offset`) is calculated based on the UTF-8 encoding of the input. C++ and Go are examples of languages that use this encoding natively.", "Encoding-dependent information (such as `begin_offset`) is calculated based on the UTF-16 encoding of the input. Java and Javascript are examples of languages that use this encoding natively.", "Encoding-dependent information (such as `begin_offset`) is calculated based on the UTF-32 encoding of the input. Python is an example of a language that uses this encoding natively."], "type": "string"}, "features": {"$ref": "Features", "description": "The enabled features."}}, "type": "object"}, "AnnotateTextResponse": {"description": "The text annotations response message.", "id": "AnnotateTextResponse", "properties": {"documentSentiment": {"$ref": "Sentiment", "description": "The overall sentiment for the document. Populated if the user enables AnnotateTextRequest.Features.extract_document_sentiment."}, "entities": {"description": "Entities, along with their semantic information, in the input document. Populated if the user enables AnnotateTextRequest.Features.extract_entities.", "items": {"$ref": "Entity"}, "type": "array"}, "language": {"description": "The language of the text, which will be the same as the language specified in the request or, if not specified, the automatically-detected language. See Document.language field for more details.", "type": "string"}, "sentences": {"description": "Sentences in the input document. Populated if the user enables AnnotateTextRequest.Features.extract_syntax.", "items": {"$ref": "Sentence"}, "type": "array"}, "tokens": {"description": "Tokens, along with their syntactic information, in the input document. Populated if the user enables AnnotateTextRequest.Features.extract_syntax.", "items": {"$ref": "Token"}, "type": "array"}}, "type": "object"}, "DependencyEdge": {"description": "Represents dependency parse tree information for a token.", "id": "DependencyEdge", "properties": {"headTokenIndex": {"description": "Represents the head of this token in the dependency tree. This is the index of the token which has an arc going to this token. The index is the position of the token in the array of tokens returned by the API method. If this token is a root token, then the `head_token_index` is its own index.", "format": "int32", "type": "integer"}, "label": {"description": "The parse label for the token.", "enum": ["UNKNOWN", "ABBREV", "ACOMP", "ADVCL", "ADVMOD", "AMOD", "APPOS", "ATTR", "AUX", "AUXPASS", "CC", "CCOMP", "CONJ", "CSUBJ", "CSUBJPASS", "DEP", "DET", "DISCOURSE", "DOBJ", "EXPL", "GOESWITH", "IOBJ", "MARK", "MWE", "MWV", "NEG", "NN", "NPADVMOD", "NSUBJ", "NSUBJPASS", "NUM", "NUMBER", "P", "PARATAXIS", "PARTMOD", "PCOMP", "POBJ", "POSS", "POSTNEG", "PRECOMP", "PRECONJ", "PREDET", "PREF", "PREP", "PRONL", "PRT", "PS", "QUANTMOD", "RCMOD", "RCMODREL", "RDROP", "REF", "REMNANT", "REPARANDUM", "ROOT", "SNUM", "SUFF", "TMOD", "TOPIC", "VMOD", "VOCATIVE", "XCOMP", "SUFFIX", "TITLE", "ADVPHMOD", "AUXCAUS", "AUXVV", "DTMOD", "FOREIGN", "KW", "LIST", "NOMC", "NOMCSUBJ", "NOMCSUBJPASS", "NUMC", "COP", "DISLOCATED", "ASP", "GMOD", "GOBJ", "INFMOD", "MES", "NCOMP"], "enumDescriptions": ["Unknown", "Abbreviation modifier", "Adjectival complement", "Adverbial clause modifier", "Adverbial modifier", "Adjectival modifier of an NP", "Appositional modifier of an NP", "Attribute dependent of a copular verb", "Auxiliary (non-main) verb", "Passive auxiliary", "Coordinating conjunction", "Clausal complement of a verb or adjective", "Conjunct", "Clausal subject", "Clausal passive subject", "Dependency (unable to determine)", "Determiner", "Discourse", "Direct object", "Expletive", "Goes with (part of a word in a text not well edited)", "Indirect object", "Marker (word introducing a subordinate clause)", "Multi-word expression", "Multi-word verbal expression", "Negation modifier", "Noun compound modifier", "Noun phrase used as an adverbial modifier", "Nominal subject", "Passive nominal subject", "Numeric modifier of a noun", "Element of compound number", "Punctuation mark", "Parataxis relation", "Participial modifier", "The complement of a preposition is a clause", "Object of a preposition", "Possession modifier", "Postverbal negative particle", "Predicate complement", "Preconjunt", "Predeterminer", "Prefix", "Prepositional modifier", "The relationship between a verb and verbal morpheme", "Particle", "Associative or possessive marker", "Quantifier phrase modifier", "Relative clause modifier", "Complementizer in relative clause", "Ellipsis without a preceding predicate", "Referent", "<PERSON><PERSON><PERSON>", "Reparandum", "Root", "Suffix specifying a unit of number", "Suffix", "Temporal modifier", "Topic marker", "Clause headed by an infinite form of the verb that modifies a noun", "Vocative", "Open clausal complement", "Name suffix", "Name title", "Adverbial phrase modifier", "Causative auxiliary", "Helper auxiliary", "<PERSON><PERSON><PERSON> (Prenominal modifier)", "Foreign words", "Keyword", "List for chains of comparable items", "Nominalized clause", "Nominalized clausal subject", "Nominalized clausal passive", "Compound of numeric modifier", "<PERSON><PERSON><PERSON>", "Dislocated relation (for fronted/topicalized elements)", "Aspect marker", "Genitive modifier", "Genitive object", "Infinitival modifier", "Measure", "Nominal complement of a noun"], "type": "string"}}, "type": "object"}, "Document": {"description": "Represents the input to API methods.", "id": "Document", "properties": {"content": {"description": "The content of the input in string format. Cloud audit logging exempt since it is based on user data.", "type": "string"}, "gcsContentUri": {"description": "The Google Cloud Storage URI where the file content is located. This URI must be of the form: gs://bucket_name/object_name. For more details, see https://cloud.google.com/storage/docs/reference-uris. NOTE: Cloud Storage object versioning is not supported.", "type": "string"}, "language": {"description": "The language of the document (if not specified, the language is automatically detected). Both ISO and BCP-47 language codes are accepted. [Language Support](https://cloud.google.com/natural-language/docs/languages) lists currently supported languages for each API method. If the language (either specified by the caller or automatically detected) is not supported by the called API method, an `INVALID_ARGUMENT` error is returned.", "type": "string"}, "type": {"description": "Required. If the type is not set or is `TYPE_UNSPECIFIED`, returns an `INVALID_ARGUMENT` error.", "enum": ["TYPE_UNSPECIFIED", "PLAIN_TEXT", "HTML"], "enumDescriptions": ["The content type is not specified.", "Plain text", "HTML"], "type": "string"}}, "type": "object"}, "Entity": {"description": "Represents a phrase in the text that is a known entity, such as a person, an organization, or location. The API associates information, such as salience and mentions, with entities.", "id": "Entity", "properties": {"mentions": {"description": "The mentions of this entity in the input document. The API currently supports proper noun mentions.", "items": {"$ref": "EntityMention"}, "type": "array"}, "metadata": {"additionalProperties": {"type": "string"}, "description": "Metadata associated with the entity. Currently, Wikipedia URLs and Knowledge Graph MIDs are provided, if available. The associated keys are \"wikipedia_url\" and \"mid\", respectively.", "type": "object"}, "name": {"description": "The representative name for the entity.", "type": "string"}, "salience": {"description": "The salience score associated with the entity in the [0, 1.0] range. The salience score for an entity provides information about the importance or centrality of that entity to the entire document text. Scores closer to 0 are less salient, while scores closer to 1.0 are highly salient.", "format": "float", "type": "number"}, "type": {"description": "The entity type.", "enum": ["UNKNOWN", "PERSON", "LOCATION", "ORGANIZATION", "EVENT", "WORK_OF_ART", "CONSUMER_GOOD", "OTHER"], "enumDescriptions": ["Unknown", "Person", "Location", "Organization", "Event", "Work of art", "Consumer goods", "Other types"], "type": "string"}}, "type": "object"}, "EntityMention": {"description": "Represents a mention for an entity in the text. Currently, proper noun mentions are supported.", "id": "EntityMention", "properties": {"text": {"$ref": "TextSpan", "description": "The mention text."}, "type": {"description": "The type of the entity mention.", "enum": ["TYPE_UNKNOWN", "PROPER", "COMMON"], "enumDescriptions": ["Unknown", "Proper name", "Common noun (or noun compound)"], "type": "string"}}, "type": "object"}, "Features": {"description": "All available features for sentiment, syntax, and semantic analysis. Setting each one to true will enable that specific analysis for the input.", "id": "Features", "properties": {"extractDocumentSentiment": {"description": "Extract document-level sentiment.", "type": "boolean"}, "extractEntities": {"description": "Extract entities.", "type": "boolean"}, "extractSyntax": {"description": "Extract syntax information.", "type": "boolean"}}, "type": "object"}, "PartOfSpeech": {"description": "Represents part of speech information for a token.", "id": "PartOfSpeech", "properties": {"aspect": {"description": "The grammatical aspect.", "enum": ["ASPECT_UNKNOWN", "PERFECTIVE", "IMPERFECTIVE", "PROGRESSIVE"], "enumDescriptions": ["Aspect is not applicable in the analyzed language or is not predicted.", "Perfective", "Imperfective", "Progressive"], "type": "string"}, "case": {"description": "The grammatical case.", "enum": ["CASE_UNKNOWN", "ACCUSATIVE", "ADVERBIAL", "COMPLEMENTIVE", "DATIVE", "GENITIVE", "INSTRUMENTAL", "LOCATIVE", "NOMINATIVE", "OBLIQUE", "PARTITIVE", "PREPOSITIONAL", "REFLEXIVE_CASE", "RELATIVE_CASE", "VOCATIVE"], "enumDescriptions": ["Case is not applicable in the analyzed language or is not predicted.", "Accusative", "Adverbial", "Complementive", "Dative", "Genitive", "Instrumental", "Locative", "Nominative", "Oblique", "Partitive", "Prepositional", "Reflexive", "Relative", "Vocative"], "type": "string"}, "form": {"description": "The grammatical form.", "enum": ["FORM_UNKNOWN", "ADNOMIAL", "AUXILIARY", "COMPLEMENTIZER", "FINAL_ENDING", "GERUND", "REALIS", "IRREALIS", "SHORT", "LONG", "ORDER", "SPECIFIC"], "enumDescriptions": ["Form is not applicable in the analyzed language or is not predicted.", "Adnomial", "Auxiliary", "Complementizer", "Final ending", "<PERSON><PERSON><PERSON>", "Realis", "<PERSON><PERSON><PERSON>", "Short form", "Long form", "Order form", "Specific form"], "type": "string"}, "gender": {"description": "The grammatical gender.", "enum": ["GENDER_UNKNOWN", "FEMININE", "MASCULINE", "NEUTER"], "enumDescriptions": ["Gender is not applicable in the analyzed language or is not predicted.", "Feminine", "Ma<PERSON><PERSON><PERSON>", "Neuter"], "type": "string"}, "mood": {"description": "The grammatical mood.", "enum": ["MOOD_UNKNOWN", "CONDITIONAL_MOOD", "IMPERATIVE", "INDICATIVE", "INTERROGATIVE", "JUSSIVE", "SUBJUNCTIVE"], "enumDescriptions": ["Mood is not applicable in the analyzed language or is not predicted.", "Conditional", "Imperative", "Indicative", "Interrogative", "<PERSON><PERSON><PERSON>", "Subjunctive"], "type": "string"}, "number": {"description": "The grammatical number.", "enum": ["NUMBER_UNKNOWN", "SINGULAR", "PLURAL", "DUAL"], "enumDescriptions": ["Number is not applicable in the analyzed language or is not predicted.", "Singular", "Plural", "Dual"], "type": "string"}, "person": {"description": "The grammatical person.", "enum": ["PERSON_UNKNOWN", "FIRST", "SECOND", "THIRD", "REFLEXIVE_PERSON"], "enumDescriptions": ["Person is not applicable in the analyzed language or is not predicted.", "First", "Second", "Third", "Reflexive"], "type": "string"}, "proper": {"description": "The grammatical properness.", "enum": ["PROPER_UNKNOWN", "PROPER", "NOT_PROPER"], "enumDescriptions": ["Proper is not applicable in the analyzed language or is not predicted.", "Proper", "Not proper"], "type": "string"}, "reciprocity": {"description": "The grammatical reciprocity.", "enum": ["RECIPROCITY_UNKNOWN", "RECIPROCAL", "NON_RECIPROCAL"], "enumDescriptions": ["Reciprocity is not applicable in the analyzed language or is not predicted.", "Reciprocal", "Non-reciprocal"], "type": "string"}, "tag": {"description": "The part of speech tag.", "enum": ["UNKNOWN", "ADJ", "ADP", "ADV", "CONJ", "DET", "NOUN", "NUM", "PRON", "PRT", "PUNCT", "VERB", "X", "AFFIX"], "enumDescriptions": ["Unknown", "Adjective", "Adposition (preposition and postposition)", "Adverb", "Conjunction", "Determiner", "Noun (common and proper)", "Cardinal number", "Pronoun", "Particle or other function word", "Punctuation", "Verb (all tenses and modes)", "Other: foreign words, typos, abbreviations", "Affix"], "type": "string"}, "tense": {"description": "The grammatical tense.", "enum": ["TENSE_UNKNOWN", "CONDITIONAL_TENSE", "FUTURE", "PAST", "PRESENT", "IMPERFECT", "PLUPERFECT"], "enumDescriptions": ["Tense is not applicable in the analyzed language or is not predicted.", "Conditional", "Future", "Past", "Present", "Imperfect", "Pluperfect"], "type": "string"}, "voice": {"description": "The grammatical voice.", "enum": ["VOICE_UNKNOWN", "ACTIVE", "CAUSATIVE", "PASSIVE"], "enumDescriptions": ["Voice is not applicable in the analyzed language or is not predicted.", "Active", "Causative", "Passive"], "type": "string"}}, "type": "object"}, "Sentence": {"description": "Represents a sentence in the input document.", "id": "Sentence", "properties": {"sentiment": {"$ref": "Sentiment", "description": "For calls to AnalyzeSentiment or if AnnotateTextRequest.Features.extract_document_sentiment is set to true, this field will contain the sentiment for the sentence."}, "text": {"$ref": "TextSpan", "description": "The sentence text."}}, "type": "object"}, "Sentiment": {"description": "Represents the feeling associated with the entire text or entities in the text.", "id": "Sentiment", "properties": {"magnitude": {"description": "A non-negative number in the [0, +inf) range, which represents the absolute magnitude of sentiment regardless of score (positive or negative).", "format": "float", "type": "number"}, "polarity": {"description": "DEPRECATED FIELD - This field is being deprecated in favor of score. Please refer to our documentation at https://cloud.google.com/natural-language/docs for more information.", "format": "float", "type": "number"}, "score": {"description": "Sentiment score between -1.0 (negative sentiment) and 1.0 (positive sentiment).", "format": "float", "type": "number"}}, "type": "object"}, "Status": {"description": "The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each `Status` message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors).", "id": "Status", "properties": {"code": {"description": "The status code, which should be an enum value of google.rpc.Code.", "format": "int32", "type": "integer"}, "details": {"description": "A list of messages that carry the error details. There is a common set of message types for APIs to use.", "items": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "type": "object"}, "type": "array"}, "message": {"description": "A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the google.rpc.Status.details field, or localized by the client.", "type": "string"}}, "type": "object"}, "TextSpan": {"description": "Represents an output piece of text.", "id": "TextSpan", "properties": {"beginOffset": {"description": "The API calculates the beginning offset of the content in the original document according to the EncodingType specified in the API request.", "format": "int32", "type": "integer"}, "content": {"description": "The content of the output text.", "type": "string"}}, "type": "object"}, "Token": {"description": "Represents the smallest syntactic building block of the text.", "id": "Token", "properties": {"dependencyEdge": {"$ref": "DependencyEdge", "description": "Dependency tree parse for this token."}, "lemma": {"description": "[Lemma](https://en.wikipedia.org/wiki/Lemma_%28morphology%29) of the token.", "type": "string"}, "partOfSpeech": {"$ref": "PartOfSpeech", "description": "Parts of speech tag for this token."}, "text": {"$ref": "TextSpan", "description": "The token text."}}, "type": "object"}}, "servicePath": "", "title": "Cloud Natural Language API", "version": "v1beta1", "version_module": true}