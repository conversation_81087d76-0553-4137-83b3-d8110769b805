import streamlit as st
import use_case1
import use_case2

st.title("BOM Automation Demo")

use_case = st.selectbox("Select Use Case", ["Use Case 1: Standardization", "Use Case 2: Duplicate Detection"])

uploaded_file = st.file_uploader("Upload your BOM Excel file", type=["xlsx"])

if uploaded_file:
    try:
        if use_case == "Use Case 1: Standardization":
            result = use_case1.run(uploaded_file)
        else:
            result = use_case2.run(uploaded_file)

        st.success(f"{use_case} processed successfully!")
        st.dataframe(result)

        # Option to download result
        st.download_button(
            label="Download Processed File",
            data=result.to_csv(index=False).encode("utf-8"),
            file_name="processed_bom.csv",
            mime="text/csv",
        )

    except Exception as e:
        st.error(f"Error processing file: {e}")
