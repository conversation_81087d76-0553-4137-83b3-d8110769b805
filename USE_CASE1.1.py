import pandas as pd
import re
from sentence_transformers import SentenceTransformer, util

# ---------- Step 1: Load Data ----------
def load_master_db(file_path):
    try:
        df = pd.read_csv(file_path)
    except FileNotFoundError:
        columns = ["part_number", "description", "quantity", "unit", "supplier", "price_usd", "notes"]
        df = pd.DataFrame(columns=columns)
    
    # Normalize column names
    df.columns = [col.strip().lower().replace(" ", "_") for col in df.columns]

    # Ensure description column exists
    if "description" not in df.columns:
        for col in df.columns:
            if "desc" in col:
                df.rename(columns={col: "description"}, inplace=True)
                break

    print("Master DB columns:", df.columns.tolist())
    return df

def load_bom(file_path):
    df = pd.read_excel(file_path)
    df.columns = [col.strip().lower().replace(" ", "_") for col in df.columns]

    # Ensure description column exists
    if "description" not in df.columns:
        for col in df.columns:
            if "desc" in col:
                df.rename(columns={col: "description"}, inplace=True)
                break

    print("BOM columns:", df.columns.tolist())
    return df

# ---------- Step 2: Clean Text ----------
def clean_text(text):
    if pd.isna(text):
        return ""
    text = str(text).strip().lower()
    text = re.sub(r"[^a-z0-9.\sΩµuf-]", "", text)  # keep alphanumerics, Ω, µ, uF, dashes
    return text

# ---------- Step 3: Semantic Similarity ----------
model = SentenceTransformer("all-MiniLM-L6-v2")

def semantic_match(bom_desc, master_db):
    if master_db.empty:
        return None, 0  # no matches if master DB is empty
    
    if "description" not in master_db.columns:
        raise ValueError("Column 'description' not found in master_db")

    bom_embedding = model.encode(clean_text(bom_desc), convert_to_tensor=True)
    master_embeddings = model.encode(master_db["description"].astype(str).apply(clean_text).tolist(), convert_to_tensor=True)
    
    similarities = util.cos_sim(bom_embedding, master_embeddings)[0]
    best_match_idx = similarities.argmax().item()
    best_score = similarities[best_match_idx].item()
    
    return master_db.iloc[best_match_idx], best_score

# ---------- Step 4: Standardize BOM ----------
def standardize_bom(bom_df, master_db, similarity_threshold=0.85):
    standardized_rows = []
    new_entries = []

    for _, row in bom_df.iterrows():
        bom_desc = str(row["description"])
        best_match, score = semantic_match(bom_desc, master_db)

        if best_match is not None and score >= similarity_threshold:
            # Found a good match → map to Master DB
            matched_row = best_match.to_dict()
            matched_row["matched_from_masterdb"] = "Yes"
            standardized_rows.append(matched_row)
        else:
            # No good match → treat as new entry (keep BOM's Notes as-is)
            new_entry = {
                "part_number": row.get("part_number", ""),
                "description": row.get("description", ""),
                "quantity": row.get("quantity", ""),
                "unit": row.get("unit", ""),
                "supplier": row.get("supplier", ""),
                "price_usd": row.get("price_usd", ""),
                "notes": row.get("notes", ""),
                "matched_from_masterdb": "No"
            }
            standardized_rows.append(new_entry)
            new_entries.append(new_entry)

    # Add new entries to master DB (exclude matched flag)
    if new_entries:
        master_db = pd.concat(
            [master_db, pd.DataFrame(new_entries).drop(columns=["matched_from_masterdb"])],
            ignore_index=True
        )

    return pd.DataFrame(standardized_rows), master_db

# ---------- Step 5: Save Results ----------
def save_results(standardized_bom, updated_master_db):
    standardized_bom.to_excel("standardized_bom.xlsx", index=False)
    updated_master_db.to_csv("master_db_updated.csv", index=False)

# ---------- Main Pipeline ----------
def main():
    master_db = load_master_db("master_db.csv")
    bom_df = load_bom("bom_inconsistency.xlsx")

    standardized_bom, updated_master_db = standardize_bom(bom_df, master_db)

    save_results(standardized_bom, updated_master_db)
    print("✅ BOM standardized and Master DB updated successfully.")

if __name__ == "__main__":
    main()
