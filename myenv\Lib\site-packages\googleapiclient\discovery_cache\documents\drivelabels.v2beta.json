{"auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/drive.admin.labels": {"description": "See, edit, create, and delete all Google Drive labels in your organization, and see your organization's label-related admin policies"}, "https://www.googleapis.com/auth/drive.admin.labels.readonly": {"description": "See all Google Drive labels and label-related admin policies in your organization"}, "https://www.googleapis.com/auth/drive.labels": {"description": "See, edit, create, and delete your Google Drive labels"}, "https://www.googleapis.com/auth/drive.labels.readonly": {"description": "See your Google Drive labels"}}}}, "basePath": "", "baseUrl": "https://drivelabels.googleapis.com/", "batchPath": "batch", "canonicalName": "Drive Labels", "description": "An API for managing Drive Labels", "discoveryVersion": "v1", "documentationLink": "https://developers.google.com/workspace/drive/labels", "fullyEncodeReservedExpansion": true, "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "drivelabels:v2beta", "kind": "discovery#restDescription", "mtlsRootUrl": "https://drivelabels.mtls.googleapis.com/", "name": "drivelabels", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"labels": {"methods": {"create": {"description": "Creates a label. For more information, see [Create and publish a label](https://developers.google.com/workspace/drive/labels/guides/create-label).", "flatPath": "v2beta/labels", "httpMethod": "POST", "id": "drivelabels.labels.create", "parameterOrder": [], "parameters": {"languageCode": {"description": "The BCP-47 language code to use for evaluating localized field labels in response. When not specified, values in the default configured language will be used.", "location": "query", "type": "string"}, "useAdminAccess": {"description": "Set to `true` in order to use the user's admin privileges. The server will verify the user is an admin before allowing access.", "location": "query", "type": "boolean"}}, "path": "v2beta/labels", "request": {"$ref": "GoogleAppsDriveLabelsV2betaLabel"}, "response": {"$ref": "GoogleAppsDriveLabelsV2betaLabel"}, "scopes": ["https://www.googleapis.com/auth/drive.admin.labels", "https://www.googleapis.com/auth/drive.labels"]}, "delete": {"description": "Permanently deletes a label and related metadata on Drive items. For more information, see [Disable, enable, and delete a label](https://developers.google.com/workspace/drive/labels/guides/disable-delete-label). Once deleted, the label and related Drive item metadata will be deleted. Only draft labels and disabled labels may be deleted.", "flatPath": "v2beta/labels/{labelsId}", "httpMethod": "DELETE", "id": "drivelabels.labels.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Label resource name.", "location": "path", "pattern": "^labels/[^/]+$", "required": true, "type": "string"}, "useAdminAccess": {"description": "Set to `true` in order to use the user's admin credentials. The server will verify the user is an admin for the label before allowing access.", "location": "query", "type": "boolean"}, "writeControl.requiredRevisionId": {"description": "The revision ID of the label that the write request will be applied to. If this isn't the latest revision of the label, the request will not be processed and will return a 400 Bad Request error.", "location": "query", "type": "string"}}, "path": "v2beta/{+name}", "response": {"$ref": "GoogleProtobufEmpty"}, "scopes": ["https://www.googleapis.com/auth/drive.admin.labels", "https://www.googleapis.com/auth/drive.labels"]}, "delta": {"description": "Updates a single label by applying a set of update requests resulting in a new draft revision. For more information, see [Update a label](https://developers.google.com/workspace/drive/labels/guides/update-label). The batch update is all-or-nothing: If any of the update requests are invalid, no changes are applied. The resulting draft revision must be published before the changes may be used with Drive items.", "flatPath": "v2beta/labels/{labelsId}:delta", "httpMethod": "POST", "id": "drivelabels.labels.delta", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the label to update.", "location": "path", "pattern": "^labels/[^/]+$", "required": true, "type": "string"}}, "path": "v2beta/{+name}:delta", "request": {"$ref": "GoogleAppsDriveLabelsV2betaDeltaUpdateLabelRequest"}, "response": {"$ref": "GoogleAppsDriveLabelsV2betaDeltaUpdateLabelResponse"}, "scopes": ["https://www.googleapis.com/auth/drive.admin.labels", "https://www.googleapis.com/auth/drive.labels"]}, "disable": {"description": "Disable a published label. For more information, see [Disable, enable, and delete a label](https://developers.google.com/workspace/drive/labels/guides/disable-delete-label). Disabling a label will result in a new disabled published revision based on the current published revision. If there's a draft revision, a new disabled draft revision will be created based on the latest draft revision. Older draft revisions will be deleted. Once disabled, a label may be deleted with `DeleteLabel`.", "flatPath": "v2beta/labels/{labelsId}:disable", "httpMethod": "POST", "id": "drivelabels.labels.disable", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Label resource name.", "location": "path", "pattern": "^labels/[^/]+$", "required": true, "type": "string"}}, "path": "v2beta/{+name}:disable", "request": {"$ref": "GoogleAppsDriveLabelsV2betaDisableLabelRequest"}, "response": {"$ref": "GoogleAppsDriveLabelsV2betaLabel"}, "scopes": ["https://www.googleapis.com/auth/drive.admin.labels", "https://www.googleapis.com/auth/drive.labels"]}, "enable": {"description": "Enable a disabled label and restore it to its published state. For more information, see [Disable, enable, and delete a label](https://developers.google.com/workspace/drive/labels/guides/disable-delete-label). This will result in a new published revision based on the current disabled published revision. If there's an existing disabled draft revision, a new revision will be created based on that draft and will be enabled.", "flatPath": "v2beta/labels/{labelsId}:enable", "httpMethod": "POST", "id": "drivelabels.labels.enable", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Label resource name.", "location": "path", "pattern": "^labels/[^/]+$", "required": true, "type": "string"}}, "path": "v2beta/{+name}:enable", "request": {"$ref": "GoogleAppsDriveLabelsV2betaEnableLabelRequest"}, "response": {"$ref": "GoogleAppsDriveLabelsV2betaLabel"}, "scopes": ["https://www.googleapis.com/auth/drive.admin.labels", "https://www.googleapis.com/auth/drive.labels"]}, "get": {"description": "Get a label by its resource name. For more information, see [Search for labels](https://developers.google.com/workspace/drive/labels/guides/search-label). Resource name may be any of: * `labels/{id}` - See `labels/{id}@latest` * `labels/{id}@latest` - Gets the latest revision of the label. * `labels/{id}@published` - Gets the current published revision of the label. * `labels/{id}@{revision_id}` - Gets the label at the specified revision ID.", "flatPath": "v2beta/labels/{labelsId}", "httpMethod": "GET", "id": "drivelabels.labels.get", "parameterOrder": ["name"], "parameters": {"languageCode": {"description": "The BCP-47 language code to use for evaluating localized field labels. When not specified, values in the default configured language are used.", "location": "query", "type": "string"}, "name": {"description": "Required. Label resource name. May be any of: * `labels/{id}` (equivalent to labels/{id}@latest) * `labels/{id}@latest` * `labels/{id}@published` * `labels/{id}@{revision_id}`", "location": "path", "pattern": "^labels/[^/]+$", "required": true, "type": "string"}, "useAdminAccess": {"description": "Set to `true` in order to use the user's admin credentials. The server verifies that the user is an admin for the label before allowing access.", "location": "query", "type": "boolean"}, "view": {"description": "When specified, only certain fields belonging to the indicated view are returned.", "enum": ["LABEL_VIEW_BASIC", "LABEL_VIEW_FULL"], "enumDescriptions": ["Implies the field mask: `name,id,revision_id,label_type,properties.*`", "All possible fields."], "location": "query", "type": "string"}}, "path": "v2beta/{+name}", "response": {"$ref": "GoogleAppsDriveLabelsV2betaLabel"}, "scopes": ["https://www.googleapis.com/auth/drive.admin.labels", "https://www.googleapis.com/auth/drive.admin.labels.readonly", "https://www.googleapis.com/auth/drive.labels", "https://www.googleapis.com/auth/drive.labels.readonly"]}, "list": {"description": "List labels. For more information, see [Search for labels](https://developers.google.com/workspace/drive/labels/guides/search-label).", "flatPath": "v2beta/labels", "httpMethod": "GET", "id": "drivelabels.labels.list", "parameterOrder": [], "parameters": {"customer": {"description": "The customer to scope this list request to. For example: `customers/abcd1234`. If unset, will return all labels within the current customer.", "location": "query", "type": "string"}, "languageCode": {"description": "The BCP-47 language code to use for evaluating localized field labels. When not specified, values in the default configured language are used.", "location": "query", "type": "string"}, "minimumRole": {"description": "Specifies the level of access the user must have on the returned labels. The minimum role a user must have on a label. Defaults to `READER`.", "enum": ["LABEL_ROLE_UNSPECIFIED", "READER", "APPLIER", "ORGANIZER", "EDITOR"], "enumDescriptions": ["Unknown role.", "A reader can read the label and associated metadata applied to Drive items.", "An applier can write associated metadata on Drive items in which they also have write access to. Implies `READER`.", "An organizer can pin this label in shared drives they manage and add new appliers to the label.", "Editors can make any update including deleting the label which also deletes the associated Drive item metadata. Implies `APPLIER`."], "location": "query", "type": "string"}, "pageSize": {"description": "Maximum number of labels to return per page. Default: 50. Max: 200.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The token of the page to return.", "location": "query", "type": "string"}, "publishedOnly": {"description": "Whether to include only published labels in the results. * When `true`, only the current published label revisions are returned. Disabled labels are included. Returned label resource names reference the published revision (`labels/{id}/{revision_id}`). * When `false`, the current label revisions are returned, which might not be published. Returned label resource names don't reference a specific revision (`labels/{id}`).", "location": "query", "type": "boolean"}, "useAdminAccess": {"description": "Set to `true` in order to use the user's admin credentials. This will return all labels within the customer.", "location": "query", "type": "boolean"}, "view": {"description": "When specified, only certain fields belonging to the indicated view are returned.", "enum": ["LABEL_VIEW_BASIC", "LABEL_VIEW_FULL"], "enumDescriptions": ["Implies the field mask: `name,id,revision_id,label_type,properties.*`", "All possible fields."], "location": "query", "type": "string"}}, "path": "v2beta/labels", "response": {"$ref": "GoogleAppsDriveLabelsV2betaListLabelsResponse"}, "scopes": ["https://www.googleapis.com/auth/drive.admin.labels", "https://www.googleapis.com/auth/drive.admin.labels.readonly", "https://www.googleapis.com/auth/drive.labels", "https://www.googleapis.com/auth/drive.labels.readonly"]}, "publish": {"description": "Publish all draft changes to the label. Once published, the label may not return to its draft state. For more information, see [Create and publish a label](https://developers.google.com/workspace/drive/labels/guides/create-label). Publishing a label will result in a new published revision. All previous draft revisions will be deleted. Previous published revisions will be kept but are subject to automated deletion as needed. For more information, see [Label lifecycle](https://developers.google.com/workspace/drive/labels/guides/label-lifecycle). Once published, some changes are no longer permitted. Generally, any change that would invalidate or cause new restrictions on existing metadata related to the label will be rejected. For example, the following changes to a label will be rejected after the label is published: * The label cannot be directly deleted. It must be disabled first, then deleted. * `Field.FieldType` cannot be changed. * Changes to field validation options cannot reject something that was previously accepted. * Reducing the maximum entries.", "flatPath": "v2beta/labels/{labelsId}:publish", "httpMethod": "POST", "id": "drivelabels.labels.publish", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Label resource name.", "location": "path", "pattern": "^labels/[^/]+$", "required": true, "type": "string"}}, "path": "v2beta/{+name}:publish", "request": {"$ref": "GoogleAppsDriveLabelsV2betaPublishLabelRequest"}, "response": {"$ref": "GoogleAppsDriveLabelsV2betaLabel"}, "scopes": ["https://www.googleapis.com/auth/drive.admin.labels", "https://www.googleapis.com/auth/drive.labels"]}, "updateLabelCopyMode": {"description": "Updates a label's `CopyMode`. Changes to this policy aren't revisioned, don't require publishing, and take effect immediately.", "flatPath": "v2beta/labels/{labelsId}:updateLabelCopyMode", "httpMethod": "POST", "id": "drivelabels.labels.updateLabelCopyMode", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the label to update.", "location": "path", "pattern": "^labels/[^/]+$", "required": true, "type": "string"}}, "path": "v2beta/{+name}:updateLabelCopyMode", "request": {"$ref": "GoogleAppsDriveLabelsV2betaUpdateLabelCopyModeRequest"}, "response": {"$ref": "GoogleAppsDriveLabelsV2betaLabel"}, "scopes": ["https://www.googleapis.com/auth/drive.admin.labels", "https://www.googleapis.com/auth/drive.labels"]}, "updateLabelEnabledAppSettings": {"description": "Updates a label's `EnabledAppSettings`. Enabling a label in a Google Workspace app allows it to be used in that app. This change isn't revisioned, doesn't require publishing, and takes effect immediately.", "flatPath": "v2beta/labels/{labelsId}:updateLabelEnabledAppSettings", "httpMethod": "POST", "id": "drivelabels.labels.updateLabelEnabledAppSettings", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the label to update. The resource name of the label to update.", "location": "path", "pattern": "^labels/[^/]+$", "required": true, "type": "string"}}, "path": "v2beta/{+name}:updateLabelEnabledAppSettings", "request": {"$ref": "GoogleAppsDriveLabelsV2betaUpdateLabelEnabledAppSettingsRequest"}, "response": {"$ref": "GoogleAppsDriveLabelsV2betaLabel"}}, "updatePermissions": {"description": "Updates a label's permissions. If a permission for the indicated principal doesn't exist, a label permission is created, otherwise the existing permission is updated. Permissions affect the label resource as a whole, aren't revisioned, and don't require publishing.", "flatPath": "v2beta/labels/{labelsId}/permissions", "httpMethod": "PATCH", "id": "drivelabels.labels.updatePermissions", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent label resource name.", "location": "path", "pattern": "^labels/[^/]+$", "required": true, "type": "string"}, "useAdminAccess": {"description": "Set to `true` in order to use the user's admin credentials. The server will verify the user is an admin for the label before allowing access.", "location": "query", "type": "boolean"}}, "path": "v2beta/{+parent}/permissions", "request": {"$ref": "GoogleAppsDriveLabelsV2betaLabelPermission"}, "response": {"$ref": "GoogleAppsDriveLabelsV2betaLabelPermission"}, "scopes": ["https://www.googleapis.com/auth/drive.admin.labels", "https://www.googleapis.com/auth/drive.labels"]}}, "resources": {"locks": {"methods": {"list": {"description": "Lists the label locks on a label.", "flatPath": "v2beta/labels/{labelsId}/locks", "httpMethod": "GET", "id": "drivelabels.labels.locks.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Maximum number of locks to return per page. Default: 100. Max: 200.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The token of the page to return.", "location": "query", "type": "string"}, "parent": {"description": "Required. Label on which locks are applied. Format: `labels/{label}`.", "location": "path", "pattern": "^labels/[^/]+$", "required": true, "type": "string"}}, "path": "v2beta/{+parent}/locks", "response": {"$ref": "GoogleAppsDriveLabelsV2betaListLabelLocksResponse"}, "scopes": ["https://www.googleapis.com/auth/drive.admin.labels", "https://www.googleapis.com/auth/drive.admin.labels.readonly", "https://www.googleapis.com/auth/drive.labels", "https://www.googleapis.com/auth/drive.labels.readonly"]}}}, "permissions": {"methods": {"batchDelete": {"description": "Deletes label permissions. Permissions affect the label resource as a whole, aren't revisioned, and don't require publishing.", "flatPath": "v2beta/labels/{labelsId}/permissions:batchDelete", "httpMethod": "POST", "id": "drivelabels.labels.permissions.batchDelete", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent label resource name shared by all permissions being deleted. Format: `labels/{label}`. If this is set, the parent field in the `UpdateLabelPermissionRequest` messages must either be empty or match this field.", "location": "path", "pattern": "^labels/[^/]+$", "required": true, "type": "string"}}, "path": "v2beta/{+parent}/permissions:batchDelete", "request": {"$ref": "GoogleAppsDriveLabelsV2betaBatchDeleteLabelPermissionsRequest"}, "response": {"$ref": "GoogleProtobufEmpty"}, "scopes": ["https://www.googleapis.com/auth/drive.admin.labels", "https://www.googleapis.com/auth/drive.labels"]}, "batchUpdate": {"description": "Updates label permissions. If a permission for the indicated principal doesn't exist, a label permission is created, otherwise the existing permission is updated. Permissions affect the label resource as a whole, aren't revisioned, and don't require publishing.", "flatPath": "v2beta/labels/{labelsId}/permissions:batchUpdate", "httpMethod": "POST", "id": "drivelabels.labels.permissions.batchUpdate", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent label resource name shared by all permissions being updated. Format: `labels/{label}`. If this is set, the parent field in the `UpdateLabelPermissionRequest` messages must either be empty or match this field.", "location": "path", "pattern": "^labels/[^/]+$", "required": true, "type": "string"}}, "path": "v2beta/{+parent}/permissions:batchUpdate", "request": {"$ref": "GoogleAppsDriveLabelsV2betaBatchUpdateLabelPermissionsRequest"}, "response": {"$ref": "GoogleAppsDriveLabelsV2betaBatchUpdateLabelPermissionsResponse"}, "scopes": ["https://www.googleapis.com/auth/drive.admin.labels", "https://www.googleapis.com/auth/drive.labels"]}, "create": {"description": "Updates a label's permissions. If a permission for the indicated principal doesn't exist, a label permission is created, otherwise the existing permission is updated. Permissions affect the label resource as a whole, aren't revisioned, and don't require publishing.", "flatPath": "v2beta/labels/{labelsId}/permissions", "httpMethod": "POST", "id": "drivelabels.labels.permissions.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent label resource name on the label permission is created. Format: `labels/{label}`.", "location": "path", "pattern": "^labels/[^/]+$", "required": true, "type": "string"}, "useAdminAccess": {"description": "Set to `true` in order to use the user's admin credentials. The server will verify the user is an admin for the label before allowing access.", "location": "query", "type": "boolean"}}, "path": "v2beta/{+parent}/permissions", "request": {"$ref": "GoogleAppsDriveLabelsV2betaLabelPermission"}, "response": {"$ref": "GoogleAppsDriveLabelsV2betaLabelPermission"}, "scopes": ["https://www.googleapis.com/auth/drive.admin.labels", "https://www.googleapis.com/auth/drive.labels"]}, "delete": {"description": "Deletes a label's permission. Permissions affect the label resource as a whole, aren't revisioned, and don't require publishing.", "flatPath": "v2beta/labels/{labelsId}/permissions/{permissionsId}", "httpMethod": "DELETE", "id": "drivelabels.labels.permissions.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Label permission resource name.", "location": "path", "pattern": "^labels/[^/]+/permissions/[^/]+$", "required": true, "type": "string"}, "useAdminAccess": {"description": "Set to `true` in order to use the user's admin credentials. The server will verify the user is an admin for the label before allowing access.", "location": "query", "type": "boolean"}}, "path": "v2beta/{+name}", "response": {"$ref": "GoogleProtobufEmpty"}, "scopes": ["https://www.googleapis.com/auth/drive.admin.labels", "https://www.googleapis.com/auth/drive.labels"]}, "list": {"description": "Lists a label's permissions.", "flatPath": "v2beta/labels/{labelsId}/permissions", "httpMethod": "GET", "id": "drivelabels.labels.permissions.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Maximum number of permissions to return per page. Default: 50. Max: 200.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The token of the page to return.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent label resource name on which label permissions are listed. Format: `labels/{label}`.", "location": "path", "pattern": "^labels/[^/]+$", "required": true, "type": "string"}, "useAdminAccess": {"description": "Set to `true` in order to use the user's admin credentials. The server will verify the user is an admin for the label before allowing access.", "location": "query", "type": "boolean"}}, "path": "v2beta/{+parent}/permissions", "response": {"$ref": "GoogleAppsDriveLabelsV2betaListLabelPermissionsResponse"}, "scopes": ["https://www.googleapis.com/auth/drive.admin.labels", "https://www.googleapis.com/auth/drive.admin.labels.readonly", "https://www.googleapis.com/auth/drive.labels", "https://www.googleapis.com/auth/drive.labels.readonly"]}}}, "revisions": {"methods": {"updatePermissions": {"description": "Updates a label's permissions. If a permission for the indicated principal doesn't exist, a label permission is created, otherwise the existing permission is updated. Permissions affect the label resource as a whole, aren't revisioned, and don't require publishing.", "flatPath": "v2beta/labels/{labelsId}/revisions/{revisionsId}/permissions", "httpMethod": "PATCH", "id": "drivelabels.labels.revisions.updatePermissions", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent label resource name.", "location": "path", "pattern": "^labels/[^/]+/revisions/[^/]+$", "required": true, "type": "string"}, "useAdminAccess": {"description": "Set to `true` in order to use the user's admin credentials. The server will verify the user is an admin for the label before allowing access.", "location": "query", "type": "boolean"}}, "path": "v2beta/{+parent}/permissions", "request": {"$ref": "GoogleAppsDriveLabelsV2betaLabelPermission"}, "response": {"$ref": "GoogleAppsDriveLabelsV2betaLabelPermission"}, "scopes": ["https://www.googleapis.com/auth/drive.admin.labels", "https://www.googleapis.com/auth/drive.labels"]}}, "resources": {"locks": {"methods": {"list": {"description": "Lists the label locks on a label.", "flatPath": "v2beta/labels/{labelsId}/revisions/{revisionsId}/locks", "httpMethod": "GET", "id": "drivelabels.labels.revisions.locks.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Maximum number of locks to return per page. Default: 100. Max: 200.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The token of the page to return.", "location": "query", "type": "string"}, "parent": {"description": "Required. Label on which locks are applied. Format: `labels/{label}`.", "location": "path", "pattern": "^labels/[^/]+/revisions/[^/]+$", "required": true, "type": "string"}}, "path": "v2beta/{+parent}/locks", "response": {"$ref": "GoogleAppsDriveLabelsV2betaListLabelLocksResponse"}, "scopes": ["https://www.googleapis.com/auth/drive.admin.labels", "https://www.googleapis.com/auth/drive.admin.labels.readonly", "https://www.googleapis.com/auth/drive.labels", "https://www.googleapis.com/auth/drive.labels.readonly"]}}}, "permissions": {"methods": {"batchDelete": {"description": "Deletes label permissions. Permissions affect the label resource as a whole, aren't revisioned, and don't require publishing.", "flatPath": "v2beta/labels/{labelsId}/revisions/{revisionsId}/permissions:batchDelete", "httpMethod": "POST", "id": "drivelabels.labels.revisions.permissions.batchDelete", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent label resource name shared by all permissions being deleted. Format: `labels/{label}`. If this is set, the parent field in the `UpdateLabelPermissionRequest` messages must either be empty or match this field.", "location": "path", "pattern": "^labels/[^/]+/revisions/[^/]+$", "required": true, "type": "string"}}, "path": "v2beta/{+parent}/permissions:batchDelete", "request": {"$ref": "GoogleAppsDriveLabelsV2betaBatchDeleteLabelPermissionsRequest"}, "response": {"$ref": "GoogleProtobufEmpty"}, "scopes": ["https://www.googleapis.com/auth/drive.admin.labels", "https://www.googleapis.com/auth/drive.labels"]}, "batchUpdate": {"description": "Updates label permissions. If a permission for the indicated principal doesn't exist, a label permission is created, otherwise the existing permission is updated. Permissions affect the label resource as a whole, aren't revisioned, and don't require publishing.", "flatPath": "v2beta/labels/{labelsId}/revisions/{revisionsId}/permissions:batchUpdate", "httpMethod": "POST", "id": "drivelabels.labels.revisions.permissions.batchUpdate", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent label resource name shared by all permissions being updated. Format: `labels/{label}`. If this is set, the parent field in the `UpdateLabelPermissionRequest` messages must either be empty or match this field.", "location": "path", "pattern": "^labels/[^/]+/revisions/[^/]+$", "required": true, "type": "string"}}, "path": "v2beta/{+parent}/permissions:batchUpdate", "request": {"$ref": "GoogleAppsDriveLabelsV2betaBatchUpdateLabelPermissionsRequest"}, "response": {"$ref": "GoogleAppsDriveLabelsV2betaBatchUpdateLabelPermissionsResponse"}, "scopes": ["https://www.googleapis.com/auth/drive.admin.labels", "https://www.googleapis.com/auth/drive.labels"]}, "create": {"description": "Updates a label's permissions. If a permission for the indicated principal doesn't exist, a label permission is created, otherwise the existing permission is updated. Permissions affect the label resource as a whole, aren't revisioned, and don't require publishing.", "flatPath": "v2beta/labels/{labelsId}/revisions/{revisionsId}/permissions", "httpMethod": "POST", "id": "drivelabels.labels.revisions.permissions.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent label resource name on the label permission is created. Format: `labels/{label}`.", "location": "path", "pattern": "^labels/[^/]+/revisions/[^/]+$", "required": true, "type": "string"}, "useAdminAccess": {"description": "Set to `true` in order to use the user's admin credentials. The server will verify the user is an admin for the label before allowing access.", "location": "query", "type": "boolean"}}, "path": "v2beta/{+parent}/permissions", "request": {"$ref": "GoogleAppsDriveLabelsV2betaLabelPermission"}, "response": {"$ref": "GoogleAppsDriveLabelsV2betaLabelPermission"}, "scopes": ["https://www.googleapis.com/auth/drive.admin.labels", "https://www.googleapis.com/auth/drive.labels"]}, "delete": {"description": "Deletes a label's permission. Permissions affect the label resource as a whole, aren't revisioned, and don't require publishing.", "flatPath": "v2beta/labels/{labelsId}/revisions/{revisionsId}/permissions/{permissionsId}", "httpMethod": "DELETE", "id": "drivelabels.labels.revisions.permissions.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Label permission resource name.", "location": "path", "pattern": "^labels/[^/]+/revisions/[^/]+/permissions/[^/]+$", "required": true, "type": "string"}, "useAdminAccess": {"description": "Set to `true` in order to use the user's admin credentials. The server will verify the user is an admin for the label before allowing access.", "location": "query", "type": "boolean"}}, "path": "v2beta/{+name}", "response": {"$ref": "GoogleProtobufEmpty"}, "scopes": ["https://www.googleapis.com/auth/drive.admin.labels", "https://www.googleapis.com/auth/drive.labels"]}, "list": {"description": "Lists a label's permissions.", "flatPath": "v2beta/labels/{labelsId}/revisions/{revisionsId}/permissions", "httpMethod": "GET", "id": "drivelabels.labels.revisions.permissions.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Maximum number of permissions to return per page. Default: 50. Max: 200.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The token of the page to return.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent label resource name on which label permissions are listed. Format: `labels/{label}`.", "location": "path", "pattern": "^labels/[^/]+/revisions/[^/]+$", "required": true, "type": "string"}, "useAdminAccess": {"description": "Set to `true` in order to use the user's admin credentials. The server will verify the user is an admin for the label before allowing access.", "location": "query", "type": "boolean"}}, "path": "v2beta/{+parent}/permissions", "response": {"$ref": "GoogleAppsDriveLabelsV2betaListLabelPermissionsResponse"}, "scopes": ["https://www.googleapis.com/auth/drive.admin.labels", "https://www.googleapis.com/auth/drive.admin.labels.readonly", "https://www.googleapis.com/auth/drive.labels", "https://www.googleapis.com/auth/drive.labels.readonly"]}}}}}}}, "limits": {"methods": {"getLabel": {"description": "Get the constraints on the structure of a label; such as, the maximum number of fields allowed and maximum length of the label title.", "flatPath": "v2beta/limits/label", "httpMethod": "GET", "id": "drivelabels.limits.getLabel", "parameterOrder": [], "parameters": {"name": {"description": "Required. Label revision resource name must be: \"limits/label\".", "location": "query", "type": "string"}}, "path": "v2beta/limits/label", "response": {"$ref": "GoogleAppsDriveLabelsV2betaLabelLimits"}, "scopes": ["https://www.googleapis.com/auth/drive.admin.labels", "https://www.googleapis.com/auth/drive.admin.labels.readonly", "https://www.googleapis.com/auth/drive.labels", "https://www.googleapis.com/auth/drive.labels.readonly"]}}}, "users": {"methods": {"getCapabilities": {"description": "Gets the user capabilities.", "flatPath": "v2beta/users/{usersId}/capabilities", "httpMethod": "GET", "id": "drivelabels.users.getCapabilities", "parameterOrder": ["name"], "parameters": {"customer": {"description": "The customer to scope this request to. For example: `customers/abcd1234`. If unset, it will return settings within the current customer.", "location": "query", "type": "string"}, "name": {"description": "Required. The resource name of the user. Only \"users/me/capabilities\" is supported.", "location": "path", "pattern": "^users/[^/]+/capabilities$", "required": true, "type": "string"}}, "path": "v2beta/{+name}", "response": {"$ref": "GoogleAppsDriveLabelsV2betaUserCapabilities"}, "scopes": ["https://www.googleapis.com/auth/drive.admin.labels", "https://www.googleapis.com/auth/drive.admin.labels.readonly", "https://www.googleapis.com/auth/drive.labels", "https://www.googleapis.com/auth/drive.labels.readonly"]}}}}, "revision": "20250807", "rootUrl": "https://drivelabels.googleapis.com/", "schemas": {"GoogleAppsDriveLabelsV2betaBadgeColors": {"description": "The color derived from BadgeConfig and changed to the closest recommended supported color.", "id": "GoogleAppsDriveLabelsV2betaBadgeColors", "properties": {"backgroundColor": {"$ref": "GoogleTypeColor", "description": "Output only. Badge background that pairs with the foreground.", "readOnly": true}, "foregroundColor": {"$ref": "GoogleTypeColor", "description": "Output only. Badge foreground that pairs with the background.", "readOnly": true}, "soloColor": {"$ref": "GoogleTypeColor", "description": "Output only. Color that can be used for text without a background.", "readOnly": true}}, "type": "object"}, "GoogleAppsDriveLabelsV2betaBadgeConfig": {"description": "Badge status of the label.", "id": "GoogleAppsDriveLabelsV2betaBadgeConfig", "properties": {"color": {"$ref": "GoogleTypeColor", "description": "The color of the badge. When not specified, no badge is rendered. The background, foreground, and solo (light and dark mode) colors set here are changed in the Drive UI into the closest recommended supported color."}, "priorityOverride": {"description": "Override the default global priority of this badge. When set to 0, the default priority heuristic is used.", "format": "int64", "type": "string"}}, "type": "object"}, "GoogleAppsDriveLabelsV2betaBatchDeleteLabelPermissionsRequest": {"description": "Deletes one or more label permissions.", "id": "GoogleAppsDriveLabelsV2betaBatchDeleteLabelPermissionsRequest", "properties": {"requests": {"description": "Required. The request message specifying the resources to update.", "items": {"$ref": "GoogleAppsDriveLabelsV2betaDeleteLabelPermissionRequest"}, "type": "array"}, "useAdminAccess": {"description": "Set to `true` in order to use the user's admin credentials. The server will verify the user is an admin for the label before allowing access. If this is set, the `use_admin_access` field in the `DeleteLabelPermissionRequest` messages must either be empty or match this field.", "type": "boolean"}}, "type": "object"}, "GoogleAppsDriveLabelsV2betaBatchUpdateLabelPermissionsRequest": {"description": "Updates one or more label permissions.", "id": "GoogleAppsDriveLabelsV2betaBatchUpdateLabelPermissionsRequest", "properties": {"requests": {"description": "Required. The request message specifying the resources to update.", "items": {"$ref": "GoogleAppsDriveLabelsV2betaUpdateLabelPermissionRequest"}, "type": "array"}, "useAdminAccess": {"description": "Set to `true` in order to use the user's admin credentials. The server will verify the user is an admin for the label before allowing access. If this is set, the `use_admin_access` field in the `UpdateLabelPermissionRequest` messages must either be empty or match this field.", "type": "boolean"}}, "type": "object"}, "GoogleAppsDriveLabelsV2betaBatchUpdateLabelPermissionsResponse": {"description": "Response for updating one or more label permissions.", "id": "GoogleAppsDriveLabelsV2betaBatchUpdateLabelPermissionsResponse", "properties": {"permissions": {"description": "Required. Permissions updated.", "items": {"$ref": "GoogleAppsDriveLabelsV2betaLabelPermission"}, "type": "array"}}, "type": "object"}, "GoogleAppsDriveLabelsV2betaDateLimits": {"description": "Limits for date field type.", "id": "GoogleAppsDriveLabelsV2betaDateLimits", "properties": {"maxValue": {"$ref": "GoogleTypeDate", "description": "Maximum value for the date field type."}, "minValue": {"$ref": "GoogleTypeDate", "description": "Minimum value for the date field type."}}, "type": "object"}, "GoogleAppsDriveLabelsV2betaDeleteLabelPermissionRequest": {"description": "Deletes a label permission. Permissions affect the label resource as a whole, aren't revisioned, and don't require publishing.", "id": "GoogleAppsDriveLabelsV2betaDeleteLabelPermissionRequest", "properties": {"name": {"description": "Required. Label permission resource name.", "type": "string"}, "useAdminAccess": {"description": "Set to `true` in order to use the user's admin credentials. The server will verify the user is an admin for the label before allowing access.", "type": "boolean"}}, "type": "object"}, "GoogleAppsDriveLabelsV2betaDeltaUpdateLabelRequest": {"description": "The set of requests for updating aspects of a label. If any request isn't valid, no requests will be applied.", "id": "GoogleAppsDriveLabelsV2betaDeltaUpdateLabelRequest", "properties": {"languageCode": {"description": "The BCP-47 language code to use for evaluating localized field labels when `include_label_in_response` is `true`.", "type": "string"}, "requests": {"description": "A list of updates to apply to the label. Requests will be applied in the order they are specified.", "items": {"$ref": "GoogleAppsDriveLabelsV2betaDeltaUpdateLabelRequestRequest"}, "type": "array"}, "useAdminAccess": {"description": "Set to `true` in order to use the user's admin credentials. The server will verify the user is an admin for the label before allowing access.", "type": "boolean"}, "view": {"description": "When specified, only certain fields belonging to the indicated view will be returned.", "enum": ["LABEL_VIEW_BASIC", "LABEL_VIEW_FULL"], "enumDescriptions": ["Implies the field mask: `name,id,revision_id,label_type,properties.*`", "All possible fields."], "type": "string"}, "writeControl": {"$ref": "GoogleAppsDriveLabelsV2betaWriteControl", "description": "Provides control over how write requests are executed."}}, "type": "object"}, "GoogleAppsDriveLabelsV2betaDeltaUpdateLabelRequestCreateFieldRequest": {"description": "Request to create a field within a label.", "id": "GoogleAppsDriveLabelsV2betaDeltaUpdateLabelRequestCreateFieldRequest", "properties": {"field": {"$ref": "GoogleAppsDriveLabelsV2betaField", "description": "Required. Field to create."}}, "type": "object"}, "GoogleAppsDriveLabelsV2betaDeltaUpdateLabelRequestCreateSelectionChoiceRequest": {"description": "Request to create a selection choice.", "id": "GoogleAppsDriveLabelsV2betaDeltaUpdateLabelRequestCreateSelectionChoiceRequest", "properties": {"choice": {"$ref": "GoogleAppsDriveLabelsV2betaFieldSelectionOptionsChoice", "description": "Required. The choice to create."}, "fieldId": {"description": "Required. The selection field in which a choice will be created.", "type": "string"}}, "type": "object"}, "GoogleAppsDriveLabelsV2betaDeltaUpdateLabelRequestDeleteFieldRequest": {"description": "Request to delete the field.", "id": "GoogleAppsDriveLabelsV2betaDeltaUpdateLabelRequestDeleteFieldRequest", "properties": {"id": {"description": "Required. ID of the field to delete.", "type": "string"}}, "type": "object"}, "GoogleAppsDriveLabelsV2betaDeltaUpdateLabelRequestDeleteSelectionChoiceRequest": {"description": "Request to delete a choice.", "id": "GoogleAppsDriveLabelsV2betaDeltaUpdateLabelRequestDeleteSelectionChoiceRequest", "properties": {"fieldId": {"description": "Required. The selection field from which a choice will be deleted.", "type": "string"}, "id": {"description": "Required. Choice to delete.", "type": "string"}}, "type": "object"}, "GoogleAppsDriveLabelsV2betaDeltaUpdateLabelRequestDisableFieldRequest": {"description": "Request to disable the field.", "id": "GoogleAppsDriveLabelsV2betaDeltaUpdateLabelRequestDisableFieldRequest", "properties": {"disabledPolicy": {"$ref": "GoogleAppsDriveLabelsV2betaLifecycleDisabledPolicy", "description": "Required. Field disabled policy."}, "id": {"description": "Required. Key of the field to disable.", "type": "string"}, "updateMask": {"description": "The fields that should be updated. At least one field must be specified. The root `disabled_policy` is implied and should not be specified. A single `*` can be used as a short-hand for updating every field.", "format": "google-fieldmask", "type": "string"}}, "type": "object"}, "GoogleAppsDriveLabelsV2betaDeltaUpdateLabelRequestDisableSelectionChoiceRequest": {"description": "Request to disable a choice.", "id": "GoogleAppsDriveLabelsV2betaDeltaUpdateLabelRequestDisableSelectionChoiceRequest", "properties": {"disabledPolicy": {"$ref": "GoogleAppsDriveLabelsV2betaLifecycleDisabledPolicy", "description": "Required. The disabled policy to update."}, "fieldId": {"description": "Required. The selection field in which a choice will be disabled.", "type": "string"}, "id": {"description": "Required. Choice to disable.", "type": "string"}, "updateMask": {"description": "The fields that should be updated. At least one field must be specified. The root `disabled_policy` is implied and should not be specified. A single `*` can be used as a short-hand for updating every field.", "format": "google-fieldmask", "type": "string"}}, "type": "object"}, "GoogleAppsDriveLabelsV2betaDeltaUpdateLabelRequestEnableFieldRequest": {"description": "Request to enable the field.", "id": "GoogleAppsDriveLabelsV2betaDeltaUpdateLabelRequestEnableFieldRequest", "properties": {"id": {"description": "Required. ID of the field to enable.", "type": "string"}}, "type": "object"}, "GoogleAppsDriveLabelsV2betaDeltaUpdateLabelRequestEnableSelectionChoiceRequest": {"description": "Request to enable a choice.", "id": "GoogleAppsDriveLabelsV2betaDeltaUpdateLabelRequestEnableSelectionChoiceRequest", "properties": {"fieldId": {"description": "Required. The selection field in which a choice will be enabled.", "type": "string"}, "id": {"description": "Required. Choice to enable.", "type": "string"}}, "type": "object"}, "GoogleAppsDriveLabelsV2betaDeltaUpdateLabelRequestRequest": {"description": "A single kind of update to apply to a label.", "id": "GoogleAppsDriveLabelsV2betaDeltaUpdateLabelRequestRequest", "properties": {"createField": {"$ref": "GoogleAppsDriveLabelsV2betaDeltaUpdateLabelRequestCreateFieldRequest", "description": "Creates a field."}, "createSelectionChoice": {"$ref": "GoogleAppsDriveLabelsV2betaDeltaUpdateLabelRequestCreateSelectionChoiceRequest", "description": "Create a choice within a selection field."}, "deleteField": {"$ref": "GoogleAppsDriveLabelsV2betaDeltaUpdateLabelRequestDeleteFieldRequest", "description": "Deletes a field from the label."}, "deleteSelectionChoice": {"$ref": "GoogleAppsDriveLabelsV2betaDeltaUpdateLabelRequestDeleteSelectionChoiceRequest", "description": "Delete a choice within a selection field."}, "disableField": {"$ref": "GoogleAppsDriveLabelsV2betaDeltaUpdateLabelRequestDisableFieldRequest", "description": "Disables the field."}, "disableSelectionChoice": {"$ref": "GoogleAppsDriveLabelsV2betaDeltaUpdateLabelRequestDisableSelectionChoiceRequest", "description": "Disable a choice within a selection field."}, "enableField": {"$ref": "GoogleAppsDriveLabelsV2betaDeltaUpdateLabelRequestEnableFieldRequest", "description": "Enables the field."}, "enableSelectionChoice": {"$ref": "GoogleAppsDriveLabelsV2betaDeltaUpdateLabelRequestEnableSelectionChoiceRequest", "description": "Enable a choice within a selection field."}, "updateField": {"$ref": "GoogleAppsDriveLabelsV2betaDeltaUpdateLabelRequestUpdateFieldPropertiesRequest", "description": "Updates basic properties of a field."}, "updateFieldType": {"$ref": "GoogleAppsDriveLabelsV2betaDeltaUpdateLabelRequestUpdateFieldTypeRequest", "description": "Update field type and/or type options."}, "updateLabel": {"$ref": "GoogleAppsDriveLabelsV2betaDeltaUpdateLabelRequestUpdateLabelPropertiesRequest", "description": "Updates the label properties."}, "updateSelectionChoiceProperties": {"$ref": "GoogleAppsDriveLabelsV2betaDeltaUpdateLabelRequestUpdateSelectionChoicePropertiesRequest", "description": "Update a choice property within a selection field."}}, "type": "object"}, "GoogleAppsDriveLabelsV2betaDeltaUpdateLabelRequestUpdateFieldPropertiesRequest": {"description": "Request to update field properties.", "id": "GoogleAppsDriveLabelsV2betaDeltaUpdateLabelRequestUpdateFieldPropertiesRequest", "properties": {"id": {"description": "Required. The field to update.", "type": "string"}, "properties": {"$ref": "GoogleAppsDriveLabelsV2betaFieldProperties", "description": "Required. Basic field properties."}, "updateMask": {"description": "The fields that should be updated. At least one field must be specified. The root `properties` is implied and should not be specified. A single `*` can be used as a short-hand for updating every field.", "format": "google-fieldmask", "type": "string"}}, "type": "object"}, "GoogleAppsDriveLabelsV2betaDeltaUpdateLabelRequestUpdateFieldTypeRequest": {"description": "Request to change the type of a field.", "id": "GoogleAppsDriveLabelsV2betaDeltaUpdateLabelRequestUpdateFieldTypeRequest", "properties": {"dateOptions": {"$ref": "GoogleAppsDriveLabelsV2betaFieldDateOptions", "description": "Update field to Date."}, "id": {"description": "Required. The field to update.", "type": "string"}, "integerOptions": {"$ref": "GoogleAppsDriveLabelsV2betaFieldIntegerOptions", "description": "Update field to Integer."}, "selectionOptions": {"$ref": "GoogleAppsDriveLabelsV2betaFieldSelectionOptions", "description": "Update field to Selection."}, "textOptions": {"$ref": "GoogleAppsDriveLabelsV2betaFieldTextOptions", "description": "Update field to Text."}, "updateMask": {"description": "The fields that should be updated. At least one field must be specified. The root of `type_options` is implied and should not be specified. A single `*` can be used as a short-hand for updating every field.", "format": "google-fieldmask", "type": "string"}, "userOptions": {"$ref": "GoogleAppsDriveLabelsV2betaFieldUserOptions", "description": "Update field to User."}}, "type": "object"}, "GoogleAppsDriveLabelsV2betaDeltaUpdateLabelRequestUpdateLabelPropertiesRequest": {"description": "Updates basic properties of a label.", "id": "GoogleAppsDriveLabelsV2betaDeltaUpdateLabelRequestUpdateLabelPropertiesRequest", "properties": {"properties": {"$ref": "GoogleAppsDriveLabelsV2betaLabelProperties", "description": "Required. Label properties to update."}, "updateMask": {"description": "The fields that should be updated. At least one field must be specified. The root `label_properties` is implied and should not be specified. A single `*` can be used as a short-hand for updating every field.", "format": "google-fieldmask", "type": "string"}}, "type": "object"}, "GoogleAppsDriveLabelsV2betaDeltaUpdateLabelRequestUpdateSelectionChoicePropertiesRequest": {"description": "Request to update a choice property.", "id": "GoogleAppsDriveLabelsV2betaDeltaUpdateLabelRequestUpdateSelectionChoicePropertiesRequest", "properties": {"fieldId": {"description": "Required. The selection field to update.", "type": "string"}, "id": {"description": "Required. The choice to update.", "type": "string"}, "properties": {"$ref": "GoogleAppsDriveLabelsV2betaFieldSelectionOptionsChoiceProperties", "description": "Required. The choice properties to update."}, "updateMask": {"description": "The fields that should be updated. At least one field must be specified. The root `properties` is implied and should not be specified. A single `*` can be used as a short-hand for updating every field.", "format": "google-fieldmask", "type": "string"}}, "type": "object"}, "GoogleAppsDriveLabelsV2betaDeltaUpdateLabelResponse": {"description": "Response for label update.", "id": "GoogleAppsDriveLabelsV2betaDeltaUpdateLabelResponse", "properties": {"responses": {"description": "The reply of the updates. This maps 1:1 with the updates, although responses to some requests may be empty.", "items": {"$ref": "GoogleAppsDriveLabelsV2betaDeltaUpdateLabelResponseResponse"}, "type": "array"}, "updatedLabel": {"$ref": "GoogleAppsDriveLabelsV2betaLabel", "description": "The label after updates were applied. This is only set if `include_label_in_response` is `true` and there were no errors."}}, "type": "object"}, "GoogleAppsDriveLabelsV2betaDeltaUpdateLabelResponseCreateFieldResponse": {"description": "Response following field create.", "id": "GoogleAppsDriveLabelsV2betaDeltaUpdateLabelResponseCreateFieldResponse", "properties": {"id": {"description": "The field of the created field. When left blank in a create request, a key will be autogenerated and can be identified here.", "type": "string"}, "priority": {"description": "The priority of the created field. The priority may change from what was specified to assure contiguous priorities between fields (1-n).", "format": "int32", "type": "integer"}}, "type": "object"}, "GoogleAppsDriveLabelsV2betaDeltaUpdateLabelResponseCreateSelectionChoiceResponse": {"description": "Response following selection choice create.", "id": "GoogleAppsDriveLabelsV2betaDeltaUpdateLabelResponseCreateSelectionChoiceResponse", "properties": {"fieldId": {"description": "The server-generated ID of the field.", "type": "string"}, "id": {"description": "The server-generated ID of the created choice within the field.", "type": "string"}}, "type": "object"}, "GoogleAppsDriveLabelsV2betaDeltaUpdateLabelResponseDeleteFieldResponse": {"description": "Response following field delete.", "id": "GoogleAppsDriveLabelsV2betaDeltaUpdateLabelResponseDeleteFieldResponse", "properties": {}, "type": "object"}, "GoogleAppsDriveLabelsV2betaDeltaUpdateLabelResponseDeleteSelectionChoiceResponse": {"description": "Response following choice delete.", "id": "GoogleAppsDriveLabelsV2betaDeltaUpdateLabelResponseDeleteSelectionChoiceResponse", "properties": {}, "type": "object"}, "GoogleAppsDriveLabelsV2betaDeltaUpdateLabelResponseDisableFieldResponse": {"description": "Response following field disable.", "id": "GoogleAppsDriveLabelsV2betaDeltaUpdateLabelResponseDisableFieldResponse", "properties": {}, "type": "object"}, "GoogleAppsDriveLabelsV2betaDeltaUpdateLabelResponseDisableSelectionChoiceResponse": {"description": "Response following choice disable.", "id": "GoogleAppsDriveLabelsV2betaDeltaUpdateLabelResponseDisableSelectionChoiceResponse", "properties": {}, "type": "object"}, "GoogleAppsDriveLabelsV2betaDeltaUpdateLabelResponseEnableFieldResponse": {"description": "Response following field enable.", "id": "GoogleAppsDriveLabelsV2betaDeltaUpdateLabelResponseEnableFieldResponse", "properties": {}, "type": "object"}, "GoogleAppsDriveLabelsV2betaDeltaUpdateLabelResponseEnableSelectionChoiceResponse": {"description": "Response following choice enable.", "id": "GoogleAppsDriveLabelsV2betaDeltaUpdateLabelResponseEnableSelectionChoiceResponse", "properties": {}, "type": "object"}, "GoogleAppsDriveLabelsV2betaDeltaUpdateLabelResponseResponse": {"description": "A single response from an update.", "id": "GoogleAppsDriveLabelsV2betaDeltaUpdateLabelResponseResponse", "properties": {"createField": {"$ref": "GoogleAppsDriveLabelsV2betaDeltaUpdateLabelResponseCreateFieldResponse", "description": "Creates a field."}, "createSelectionChoice": {"$ref": "GoogleAppsDriveLabelsV2betaDeltaUpdateLabelResponseCreateSelectionChoiceResponse", "description": "Creates a selection list option to add to a selection field."}, "deleteField": {"$ref": "GoogleAppsDriveLabelsV2betaDeltaUpdateLabelResponseDeleteFieldResponse", "description": "Deletes a field from the label."}, "deleteSelectionChoice": {"$ref": "GoogleAppsDriveLabelsV2betaDeltaUpdateLabelResponseDeleteSelectionChoiceResponse", "description": "Deletes a choice from a selection field."}, "disableField": {"$ref": "GoogleAppsDriveLabelsV2betaDeltaUpdateLabelResponseDisableFieldResponse", "description": "Disables field."}, "disableSelectionChoice": {"$ref": "GoogleAppsDriveLabelsV2betaDeltaUpdateLabelResponseDisableSelectionChoiceResponse", "description": "Disables a choice within a selection field."}, "enableField": {"$ref": "GoogleAppsDriveLabelsV2betaDeltaUpdateLabelResponseEnableFieldResponse", "description": "Enables field."}, "enableSelectionChoice": {"$ref": "GoogleAppsDriveLabelsV2betaDeltaUpdateLabelResponseEnableSelectionChoiceResponse", "description": "Enables a choice within a selection field."}, "updateField": {"$ref": "GoogleAppsDriveLabelsV2betaDeltaUpdateLabelResponseUpdateFieldPropertiesResponse", "description": "Updates basic properties of a field."}, "updateFieldType": {"$ref": "GoogleAppsDriveLabelsV2betaDeltaUpdateLabelResponseUpdateFieldTypeResponse", "description": "Updates field type and/or type options."}, "updateLabel": {"$ref": "GoogleAppsDriveLabelsV2betaDeltaUpdateLabelResponseUpdateLabelPropertiesResponse", "description": "Updates basic properties of a label."}, "updateSelectionChoiceProperties": {"$ref": "GoogleAppsDriveLabelsV2betaDeltaUpdateLabelResponseUpdateSelectionChoicePropertiesResponse", "description": "Updates a choice within a selection field."}}, "type": "object"}, "GoogleAppsDriveLabelsV2betaDeltaUpdateLabelResponseUpdateFieldPropertiesResponse": {"description": "Response following update to field properties.", "id": "GoogleAppsDriveLabelsV2betaDeltaUpdateLabelResponseUpdateFieldPropertiesResponse", "properties": {"priority": {"description": "The priority of the updated field. The priority may change from what was specified to assure contiguous priorities between fields (1-n).", "format": "int32", "type": "integer"}}, "type": "object"}, "GoogleAppsDriveLabelsV2betaDeltaUpdateLabelResponseUpdateFieldTypeResponse": {"description": "Response following update to field type.", "id": "GoogleAppsDriveLabelsV2betaDeltaUpdateLabelResponseUpdateFieldTypeResponse", "properties": {}, "type": "object"}, "GoogleAppsDriveLabelsV2betaDeltaUpdateLabelResponseUpdateLabelPropertiesResponse": {"description": "Response following update to label properties.", "id": "GoogleAppsDriveLabelsV2betaDeltaUpdateLabelResponseUpdateLabelPropertiesResponse", "properties": {}, "type": "object"}, "GoogleAppsDriveLabelsV2betaDeltaUpdateLabelResponseUpdateSelectionChoicePropertiesResponse": {"description": "Response following update to selection choice properties.", "id": "GoogleAppsDriveLabelsV2betaDeltaUpdateLabelResponseUpdateSelectionChoicePropertiesResponse", "properties": {"priority": {"description": "The priority of the updated choice. The priority may change from what was specified to assure contiguous priorities between choices (1-n).", "format": "int32", "type": "integer"}}, "type": "object"}, "GoogleAppsDriveLabelsV2betaDisableLabelRequest": {"description": "Request to deprecate a published label.", "id": "GoogleAppsDriveLabelsV2betaDisableLabelRequest", "properties": {"disabledPolicy": {"$ref": "GoogleAppsDriveLabelsV2betaLifecycleDisabledPolicy", "description": "Disabled policy to use."}, "languageCode": {"description": "The BCP-47 language code to use for evaluating localized field labels. When not specified, values in the default configured language will be used.", "type": "string"}, "updateMask": {"description": "The fields that should be updated. At least one field must be specified. The root `disabled_policy` is implied and should not be specified. A single `*` can be used as a short-hand for updating every field.", "format": "google-fieldmask", "type": "string"}, "useAdminAccess": {"description": "Set to `true` in order to use the user's admin credentials. The server will verify the user is an admin for the label before allowing access.", "type": "boolean"}, "writeControl": {"$ref": "GoogleAppsDriveLabelsV2betaWriteControl", "description": "Provides control over how write requests are executed. Defaults to unset, which means the last write wins."}}, "type": "object"}, "GoogleAppsDriveLabelsV2betaEnableLabelRequest": {"description": "Request to enable a label.", "id": "GoogleAppsDriveLabelsV2betaEnableLabelRequest", "properties": {"languageCode": {"description": "The BCP-47 language code to use for evaluating localized field labels. When not specified, values in the default configured language will be used.", "type": "string"}, "useAdminAccess": {"description": "Set to `true` in order to use the user's admin credentials. The server will verify the user is an admin for the label before allowing access.", "type": "boolean"}, "writeControl": {"$ref": "GoogleAppsDriveLabelsV2betaWriteControl", "description": "Provides control over how write requests are executed. Defaults to unset, which means the last write wins."}}, "type": "object"}, "GoogleAppsDriveLabelsV2betaField": {"description": "Defines a field that has a display name, data type, and other configuration options. This field defines the kind of metadata that may be set on a Drive item.", "id": "GoogleAppsDriveLabelsV2betaField", "properties": {"appliedCapabilities": {"$ref": "GoogleAppsDriveLabelsV2betaFieldAppliedCapabilities", "description": "Output only. The capabilities this user has on this field and its value when the label is applied on Drive items.", "readOnly": true}, "createTime": {"description": "Output only. The time this field was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "creator": {"$ref": "GoogleAppsDriveLabelsV2betaUserInfo", "description": "Output only. The user who created this field.", "readOnly": true}, "dateOptions": {"$ref": "GoogleAppsDriveLabelsV2betaFieldDateOptions", "description": "Date field options."}, "disableTime": {"description": "Output only. The time this field was disabled. This value has no meaning when the field is not disabled.", "format": "google-datetime", "readOnly": true, "type": "string"}, "disabler": {"$ref": "GoogleAppsDriveLabelsV2betaUserInfo", "description": "Output only. The user who disabled this field. This value has no meaning when the field is not disabled.", "readOnly": true}, "displayHints": {"$ref": "GoogleAppsDriveLabelsV2betaFieldDisplayHints", "description": "Output only. UI display hints for rendering a field.", "readOnly": true}, "id": {"description": "Output only. The key of a field, unique within a label or library. This value is autogenerated. Matches the regex: `([a-zA-Z0-9])+`.", "readOnly": true, "type": "string"}, "integerOptions": {"$ref": "GoogleAppsDriveLabelsV2betaFieldIntegerOptions", "description": "Integer field options."}, "lifecycle": {"$ref": "GoogleAppsDriveLabelsV2betaLifecycle", "description": "Output only. The lifecycle of this field.", "readOnly": true}, "lockStatus": {"$ref": "GoogleAppsDriveLabelsV2betaLockStatus", "description": "Output only. The `LockStatus` of this field.", "readOnly": true}, "properties": {"$ref": "GoogleAppsDriveLabelsV2betaFieldProperties", "description": "The basic properties of the field."}, "publisher": {"$ref": "GoogleAppsDriveLabelsV2betaUserInfo", "description": "Output only. The user who published this field. This value has no meaning when the field is not published.", "readOnly": true}, "queryKey": {"description": "Output only. The key to use when constructing Drive search queries to find files based on values defined for this field on files. For example, \"`{query_key}` > 2001-01-01\".", "readOnly": true, "type": "string"}, "schemaCapabilities": {"$ref": "GoogleAppsDriveLabelsV2betaFieldSchemaCapabilities", "description": "Output only. The capabilities this user has when editing this field.", "readOnly": true}, "selectionOptions": {"$ref": "GoogleAppsDriveLabelsV2betaFieldSelectionOptions", "description": "Selection field options."}, "textOptions": {"$ref": "GoogleAppsDriveLabelsV2betaFieldTextOptions", "description": "Text field options."}, "updateTime": {"description": "Output only. The time this field was updated.", "format": "google-datetime", "readOnly": true, "type": "string"}, "updater": {"$ref": "GoogleAppsDriveLabelsV2betaUserInfo", "description": "Output only. The user who modified this field.", "readOnly": true}, "userOptions": {"$ref": "GoogleAppsDriveLabelsV2betaFieldUserOptions", "description": "User field options."}}, "type": "object"}, "GoogleAppsDriveLabelsV2betaFieldAppliedCapabilities": {"description": "The capabilities related to this field on applied metadata.", "id": "GoogleAppsDriveLabelsV2betaFieldAppliedCapabilities", "properties": {"canRead": {"description": "Whether the user can read related applied metadata on items.", "type": "boolean"}, "canSearch": {"description": "Whether the user can search for Drive items referencing this field.", "type": "boolean"}, "canWrite": {"description": "Whether the user can set this field on Drive items.", "type": "boolean"}}, "type": "object"}, "GoogleAppsDriveLabelsV2betaFieldDateOptions": {"description": "Options for the date field type.", "id": "GoogleAppsDriveLabelsV2betaFieldDateOptions", "properties": {"dateFormat": {"description": "Output only. ICU date format.", "readOnly": true, "type": "string"}, "dateFormatType": {"description": "Localized date formatting option. Field values are rendered in this format according to their locale.", "enum": ["DATE_FORMAT_UNSPECIFIED", "LONG_DATE", "SHORT_DATE"], "enumDescriptions": ["Date format unspecified.", "Includes full month name. For example, January 12, 1999 (MMMM d, y)", "Short, numeric, representation. For example, 12/13/99 (M/d/yy)"], "type": "string"}, "maxValue": {"$ref": "GoogleTypeDate", "description": "Output only. Maximum valid value (year, month, day).", "readOnly": true}, "minValue": {"$ref": "GoogleTypeDate", "description": "Output only. Minimum valid value (year, month, day).", "readOnly": true}}, "type": "object"}, "GoogleAppsDriveLabelsV2betaFieldDisplayHints": {"description": "UI display hints for rendering a field.", "id": "GoogleAppsDriveLabelsV2betaFieldDisplayHints", "properties": {"disabled": {"description": "Whether the field should be shown in the UI as disabled.", "type": "boolean"}, "hiddenInSearch": {"description": "This field should be hidden in the search menu when searching for Drive items.", "type": "boolean"}, "required": {"description": "Whether the field should be shown as required in the UI.", "type": "boolean"}, "shownInApply": {"description": "This field should be shown in the apply menu when applying values to a Drive item.", "type": "boolean"}}, "type": "object"}, "GoogleAppsDriveLabelsV2betaFieldIntegerOptions": {"description": "Options for the Integer field type.", "id": "GoogleAppsDriveLabelsV2betaFieldIntegerOptions", "properties": {"maxValue": {"description": "Output only. The maximum valid value for the integer field.", "format": "int64", "readOnly": true, "type": "string"}, "minValue": {"description": "Output only. The minimum valid value for the integer field.", "format": "int64", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleAppsDriveLabelsV2betaFieldLimits": {"description": "Field constants governing the structure of a field; such as, the maximum title length, minimum and maximum field values or length, etc.", "id": "GoogleAppsDriveLabelsV2betaFieldLimits", "properties": {"dateLimits": {"$ref": "GoogleAppsDriveLabelsV2betaDateLimits", "description": "Date field limits."}, "integerLimits": {"$ref": "GoogleAppsDriveLabelsV2betaIntegerLimits", "description": "Integer field limits."}, "longTextLimits": {"$ref": "GoogleAppsDriveLabelsV2betaLongTextLimits", "description": "Long text field limits."}, "maxDescriptionLength": {"description": "Limits for field description, also called help text.", "format": "int32", "type": "integer"}, "maxDisplayNameLength": {"description": "Limits for field title.", "format": "int32", "type": "integer"}, "maxIdLength": {"description": "Maximum length for the id.", "format": "int32", "type": "integer"}, "selectionLimits": {"$ref": "GoogleAppsDriveLabelsV2betaSelectionLimits", "description": "Selection field limits."}, "textLimits": {"$ref": "GoogleAppsDriveLabelsV2betaTextLimits", "description": "The relevant limits for the specified Field.Type. Text field limits."}, "userLimits": {"$ref": "GoogleAppsDriveLabelsV2betaUserLimits", "description": "User field limits."}}, "type": "object"}, "GoogleAppsDriveLabelsV2betaFieldListOptions": {"description": "Options for a multi-valued variant of an associated field type.", "id": "GoogleAppsDriveLabelsV2betaFieldListOptions", "properties": {"maxEntries": {"description": "Maximum number of entries permitted.", "format": "int32", "type": "integer"}}, "type": "object"}, "GoogleAppsDriveLabelsV2betaFieldProperties": {"description": "The basic properties of the field.", "id": "GoogleAppsDriveLabelsV2betaFieldProperties", "properties": {"displayName": {"description": "Required. The display text to show in the UI identifying this field.", "type": "string"}, "insertBeforeField": {"description": "Input only. Insert or move this field before the indicated field. If empty, the field is placed at the end of the list.", "type": "string"}, "required": {"description": "Whether the field should be marked as required.", "type": "boolean"}}, "type": "object"}, "GoogleAppsDriveLabelsV2betaFieldSchemaCapabilities": {"description": "The capabilities related to this field when editing the field.", "id": "GoogleAppsDriveLabelsV2betaFieldSchemaCapabilities", "properties": {"canDelete": {"description": "Whether the user can delete this field. The user must have permission and the field must be deprecated.", "type": "boolean"}, "canDisable": {"description": "Whether the user can disable this field. The user must have permission and this field must not already be disabled.", "type": "boolean"}, "canEnable": {"description": "Whether the user can enable this field. The user must have permission and this field must be disabled.", "type": "boolean"}, "canUpdate": {"description": "Whether the user can change this field.", "type": "boolean"}}, "type": "object"}, "GoogleAppsDriveLabelsV2betaFieldSelectionOptions": {"description": "Options for the selection field type.", "id": "GoogleAppsDriveLabelsV2betaFieldSelectionOptions", "properties": {"choices": {"description": "The options available for this selection field. The list order is consistent, and modified with `insert_before_choice`.", "items": {"$ref": "GoogleAppsDriveLabelsV2betaFieldSelectionOptionsChoice"}, "type": "array"}, "listOptions": {"$ref": "GoogleAppsDriveLabelsV2betaFieldListOptions", "description": "When specified, indicates this field supports a list of values. Once the field is published, this cannot be changed."}}, "type": "object"}, "GoogleAppsDriveLabelsV2betaFieldSelectionOptionsChoice": {"description": "Selection field choice.", "id": "GoogleAppsDriveLabelsV2betaFieldSelectionOptionsChoice", "properties": {"appliedCapabilities": {"$ref": "GoogleAppsDriveLabelsV2betaFieldSelectionOptionsChoiceAppliedCapabilities", "description": "Output only. The capabilities related to this choice on applied metadata.", "readOnly": true}, "createTime": {"description": "Output only. The time this choice was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "creator": {"$ref": "GoogleAppsDriveLabelsV2betaUserInfo", "description": "Output only. The user who created this choice.", "readOnly": true}, "disableTime": {"description": "Output only. The time this choice was disabled. This value has no meaning when the choice is not disabled.", "format": "google-datetime", "readOnly": true, "type": "string"}, "disabler": {"$ref": "GoogleAppsDriveLabelsV2betaUserInfo", "description": "Output only. The user who disabled this choice. This value has no meaning when the option is not disabled.", "readOnly": true}, "displayHints": {"$ref": "GoogleAppsDriveLabelsV2betaFieldSelectionOptionsChoiceDisplayHints", "description": "Output only. UI display hints for rendering a choice.", "readOnly": true}, "id": {"description": "The unique value of the choice. This ID is autogenerated. Matches the regex: `([a-zA-Z0-9_])+`.", "type": "string"}, "lifecycle": {"$ref": "GoogleAppsDriveLabelsV2betaLifecycle", "description": "Output only. Lifecycle of the choice.", "readOnly": true}, "lockStatus": {"$ref": "GoogleAppsDriveLabelsV2betaLockStatus", "description": "Output only. The `LockStatus` of this choice.", "readOnly": true}, "properties": {"$ref": "GoogleAppsDriveLabelsV2betaFieldSelectionOptionsChoiceProperties", "description": "Basic properties of the choice."}, "publishTime": {"description": "Output only. The time this choice was published. This value has no meaning when the choice is not published.", "format": "google-datetime", "readOnly": true, "type": "string"}, "publisher": {"$ref": "GoogleAppsDriveLabelsV2betaUserInfo", "description": "Output only. The user who published this choice. This value has no meaning when the choice is not published.", "readOnly": true}, "schemaCapabilities": {"$ref": "GoogleAppsDriveLabelsV2betaFieldSelectionOptionsChoiceSchemaCapabilities", "description": "Output only. The capabilities related to this option when editing the option.", "readOnly": true}, "updateTime": {"description": "Output only. The time this choice was updated last.", "format": "google-datetime", "readOnly": true, "type": "string"}, "updater": {"$ref": "GoogleAppsDriveLabelsV2betaUserInfo", "description": "Output only. The user who updated this choice last.", "readOnly": true}}, "type": "object"}, "GoogleAppsDriveLabelsV2betaFieldSelectionOptionsChoiceAppliedCapabilities": {"description": "The capabilities related to this choice on applied metadata.", "id": "GoogleAppsDriveLabelsV2betaFieldSelectionOptionsChoiceAppliedCapabilities", "properties": {"canRead": {"description": "Whether the user can read related applied metadata on items.", "type": "boolean"}, "canSearch": {"description": "Whether the user can use this choice in search queries.", "type": "boolean"}, "canSelect": {"description": "Whether the user can select this choice on an item.", "type": "boolean"}}, "type": "object"}, "GoogleAppsDriveLabelsV2betaFieldSelectionOptionsChoiceDisplayHints": {"description": "UI display hints for rendering an option.", "id": "GoogleAppsDriveLabelsV2betaFieldSelectionOptionsChoiceDisplayHints", "properties": {"badgeColors": {"$ref": "GoogleAppsDriveLabelsV2betaBadgeColors", "description": "The colors to use for the badge. Changed to Google Material colors based on the chosen `properties.badge_config.color`."}, "badgePriority": {"description": "The priority of this badge. Used to compare and sort between multiple badges. A lower number means the badge should be shown first. When a badging configuration is not present, this will be 0. Otherwise, this will be set to `BadgeConfig.priority_override` or the default heuristic which prefers creation date of the label, and field and option priority.", "format": "int64", "type": "string"}, "darkBadgeColors": {"$ref": "GoogleAppsDriveLabelsV2betaBadgeColors", "description": "The dark-mode color to use for the badge. Changed to Google Material colors based on the chosen `properties.badge_config.color`."}, "disabled": {"description": "Whether the option should be shown in the UI as disabled.", "type": "boolean"}, "hiddenInSearch": {"description": "This option should be hidden in the search menu when searching for Drive items.", "type": "boolean"}, "shownInApply": {"description": "This option should be shown in the apply menu when applying values to a Drive item.", "type": "boolean"}}, "type": "object"}, "GoogleAppsDriveLabelsV2betaFieldSelectionOptionsChoiceProperties": {"description": "Basic properties of the choice.", "id": "GoogleAppsDriveLabelsV2betaFieldSelectionOptionsChoiceProperties", "properties": {"badgeConfig": {"$ref": "GoogleAppsDriveLabelsV2betaBadgeConfig", "description": "The badge configuration for this choice. When set, the label that owns this choice is considered a \"badged label\"."}, "description": {"description": "The description of this label.", "type": "string"}, "displayName": {"description": "Required. The display text to show in the UI identifying this field.", "type": "string"}, "insertBeforeChoice": {"description": "Input only. Insert or move this choice before the indicated choice. If empty, the choice is placed at the end of the list.", "type": "string"}}, "type": "object"}, "GoogleAppsDriveLabelsV2betaFieldSelectionOptionsChoiceSchemaCapabilities": {"description": "The capabilities related to this choice when editing the choice.", "id": "GoogleAppsDriveLabelsV2betaFieldSelectionOptionsChoiceSchemaCapabilities", "properties": {"canDelete": {"description": "Whether the user can delete this choice.", "type": "boolean"}, "canDisable": {"description": "Whether the user can disable this choice.", "type": "boolean"}, "canEnable": {"description": "Whether the user can enable this choice.", "type": "boolean"}, "canUpdate": {"description": "Whether the user can update this choice.", "type": "boolean"}}, "type": "object"}, "GoogleAppsDriveLabelsV2betaFieldTextOptions": {"description": "Options for the Text field type.", "id": "GoogleAppsDriveLabelsV2betaFieldTextOptions", "properties": {"maxLength": {"description": "Output only. The maximum valid length of values for the text field.", "format": "int32", "readOnly": true, "type": "integer"}, "minLength": {"description": "Output only. The minimum valid length of values for the text field.", "format": "int32", "readOnly": true, "type": "integer"}}, "type": "object"}, "GoogleAppsDriveLabelsV2betaFieldUserOptions": {"description": "Options for the user field type.", "id": "GoogleAppsDriveLabelsV2betaFieldUserOptions", "properties": {"listOptions": {"$ref": "GoogleAppsDriveLabelsV2betaFieldListOptions", "description": "When specified, indicates that this field supports a list of values. Once the field is published, this cannot be changed."}}, "type": "object"}, "GoogleAppsDriveLabelsV2betaIntegerLimits": {"description": "Limits for integer field type.", "id": "GoogleAppsDriveLabelsV2betaIntegerLimits", "properties": {"maxValue": {"description": "Maximum value for an integer field type.", "format": "int64", "type": "string"}, "minValue": {"description": "Minimum value for an integer field type.", "format": "int64", "type": "string"}}, "type": "object"}, "GoogleAppsDriveLabelsV2betaLabel": {"description": "A label defines a taxonomy that can be applied to Drive items in order to organize and search across items. Labels can be simple strings, or can contain fields that describe additional metadata that can be further used to organize and search Drive items.", "id": "GoogleAppsDriveLabelsV2betaLabel", "properties": {"appliedCapabilities": {"$ref": "GoogleAppsDriveLabelsV2betaLabelAppliedCapabilities", "description": "Output only. The capabilities related to this label on applied metadata.", "readOnly": true}, "appliedLabelPolicy": {"$ref": "GoogleAppsDriveLabelsV2betaLabelAppliedLabelPolicy", "description": "Output only. Behavior of this label when it's applied to Drive items.", "readOnly": true}, "createTime": {"description": "Output only. The time this label was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "creator": {"$ref": "GoogleAppsDriveLabelsV2betaUserInfo", "description": "Output only. The user who created this label.", "readOnly": true}, "customer": {"description": "Output only. The customer this label belongs to. For example: `customers/123abc789`.", "readOnly": true, "type": "string"}, "disableTime": {"description": "Output only. The time this label was disabled. This value has no meaning when the label isn't disabled.", "format": "google-datetime", "readOnly": true, "type": "string"}, "disabler": {"$ref": "GoogleAppsDriveLabelsV2betaUserInfo", "description": "Output only. The user who disabled this label. This value has no meaning when the label isn't disabled.", "readOnly": true}, "displayHints": {"$ref": "GoogleAppsDriveLabelsV2betaLabelDisplayHints", "description": "Output only. UI display hints for rendering the label.", "readOnly": true}, "enabledAppSettings": {"$ref": "GoogleAppsDriveLabelsV2betaLabelEnabledAppSettings", "description": "Optional. The `EnabledAppSettings` for this Label."}, "fields": {"description": "List of fields in descending priority order.", "items": {"$ref": "GoogleAppsDriveLabelsV2betaField"}, "type": "array"}, "id": {"description": "Output only. Globally unique identifier of this label. ID makes up part of the label `name`, but unlike `name`, ID is consistent between revisions. Matches the regex: `([a-zA-Z0-9])+`.", "readOnly": true, "type": "string"}, "labelType": {"description": "Required. The type of label.", "enum": ["LABEL_TYPE_UNSPECIFIED", "SHARED", "ADMIN", "GOOGLE_APP"], "enumDescriptions": ["Unknown label type.", "Shared labels may be shared with users to apply to Drive items.", "Admin-owned label. Only creatable and editable by admins. Supports some additional admin-only features.", "A label owned by an internal Google application rather than a customer. These labels are read-only."], "type": "string"}, "learnMoreUri": {"description": "Custom URL to present to users to allow them to learn more about this label and how it should be used.", "type": "string"}, "lifecycle": {"$ref": "GoogleAppsDriveLabelsV2betaLifecycle", "description": "Output only. The lifecycle state of the label including whether it's published, deprecated, and has draft changes.", "readOnly": true}, "lockStatus": {"$ref": "GoogleAppsDriveLabelsV2betaLockStatus", "description": "Output only. The `LockStatus` of this label.", "readOnly": true}, "name": {"description": "Output only. Resource name of the label. Will be in the form of either: `labels/{id}` or `labels/{id}@{revision_id}` depending on the request. See `id` and `revision_id` below.", "readOnly": true, "type": "string"}, "properties": {"$ref": "GoogleAppsDriveLabelsV2betaLabelProperties", "description": "Required. The basic properties of the label."}, "publishTime": {"description": "Output only. The time this label was published. This value has no meaning when the label isn't published.", "format": "google-datetime", "readOnly": true, "type": "string"}, "publisher": {"$ref": "GoogleAppsDriveLabelsV2betaUserInfo", "description": "Output only. The user who published this label. This value has no meaning when the label isn't published.>>", "readOnly": true}, "revisionCreateTime": {"description": "Output only. The time this label revision was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "revisionCreator": {"$ref": "GoogleAppsDriveLabelsV2betaUserInfo", "description": "Output only. The user who created this label revision.", "readOnly": true}, "revisionId": {"description": "Output only. Revision ID of the label. Revision ID might be part of the label `name` depending on the request issued. A new revision is created whenever revisioned properties of a label are changed. Matches the regex: `([a-zA-Z0-9])+`.", "readOnly": true, "type": "string"}, "schemaCapabilities": {"$ref": "GoogleAppsDriveLabelsV2betaLabelSchemaCapabilities", "description": "Output only. The capabilities the user has on this label.", "readOnly": true}}, "type": "object"}, "GoogleAppsDriveLabelsV2betaLabelAppliedCapabilities": {"description": "The capabilities a user has on this label's applied metadata.", "id": "GoogleAppsDriveLabelsV2betaLabelAppliedCapabilities", "properties": {"canApply": {"description": "Whether the user can apply this label to items.", "type": "boolean"}, "canRead": {"description": "Whether the user can read applied metadata related to this label.", "type": "boolean"}, "canRemove": {"description": "Whether the user can remove this label from items.", "type": "boolean"}}, "type": "object"}, "GoogleAppsDriveLabelsV2betaLabelAppliedLabelPolicy": {"description": "Behavior of this label when it's applied to Drive items.", "id": "GoogleAppsDriveLabelsV2betaLabelAppliedLabelPolicy", "properties": {"copyMode": {"description": "Indicates how the applied label and field values should be copied when a Drive item is copied.", "enum": ["COPY_MODE_UNSPECIFIED", "DO_NOT_COPY", "ALWAYS_COPY", "COPY_APPLIABLE"], "enumDescriptions": ["Copy mode unspecified.", "The applied label and field values aren't copied by default when the Drive item it's applied to is copied.", "The applied label and field values are always copied when the Drive item it's applied to is copied. Only admins can use this mode.", "The applied label and field values are copied if the label is appliable by the user making the copy."], "type": "string"}}, "type": "object"}, "GoogleAppsDriveLabelsV2betaLabelDisplayHints": {"description": "The UI display hints for rendering the label.", "id": "GoogleAppsDriveLabelsV2betaLabelDisplayHints", "properties": {"disabled": {"description": "Whether the label should be shown in the UI as disabled.", "type": "boolean"}, "hiddenInSearch": {"description": "This label should be hidden in the search menu when searching for Drive items.", "type": "boolean"}, "priority": {"description": "The order to display labels in a list.", "format": "int64", "type": "string"}, "shownInApply": {"description": "This label should be shown in the apply menu when applying values to a Drive item.", "type": "boolean"}}, "type": "object"}, "GoogleAppsDriveLabelsV2betaLabelEnabledAppSettings": {"description": "Describes the Google Workspace apps in which the label can be used.", "id": "GoogleAppsDriveLabelsV2betaLabelEnabledAppSettings", "properties": {"enabledApps": {"description": "Optional. The list of apps where the label can be used.", "items": {"$ref": "GoogleAppsDriveLabelsV2betaLabelEnabledAppSettingsEnabledApp"}, "type": "array"}}, "type": "object"}, "GoogleAppsDriveLabelsV2betaLabelEnabledAppSettingsEnabledApp": {"description": "An app where the label can be used.", "id": "GoogleAppsDriveLabelsV2betaLabelEnabledAppSettingsEnabledApp", "properties": {"app": {"description": "Optional. The name of the app.", "enum": ["APP_UNSPECIFIED", "DRIVE", "GMAIL"], "enumDescriptions": ["Unspecified", "Drive", "Gmail"], "type": "string"}}, "type": "object"}, "GoogleAppsDriveLabelsV2betaLabelLimits": {"description": "Label constraints governing the structure of a label; such as, the maximum number of fields allowed and maximum length of the label title.", "id": "GoogleAppsDriveLabelsV2betaLabelLimits", "properties": {"fieldLimits": {"$ref": "GoogleAppsDriveLabelsV2betaFieldLimits", "description": "The limits for fields."}, "maxDeletedFields": {"description": "The maximum number of published fields that can be deleted.", "format": "int32", "type": "integer"}, "maxDescriptionLength": {"description": "The maximum number of characters allowed for the description.", "format": "int32", "type": "integer"}, "maxDraftRevisions": {"description": "The maximum number of draft revisions that will be kept before deleting old drafts.", "format": "int32", "type": "integer"}, "maxFields": {"description": "The maximum number of fields allowed within the label.", "format": "int32", "type": "integer"}, "maxTitleLength": {"description": "The maximum number of characters allowed for the title.", "format": "int32", "type": "integer"}, "name": {"description": "Resource name.", "type": "string"}}, "type": "object"}, "GoogleAppsDriveLabelsV2betaLabelLock": {"description": "A lock that can be applied to a label, field, or choice.", "id": "GoogleAppsDriveLabelsV2betaLabelLock", "properties": {"capabilities": {"$ref": "GoogleAppsDriveLabelsV2betaLabelLockCapabilities", "description": "Output only. The user's capabilities on this label lock.", "readOnly": true}, "choiceId": {"description": "The ID of the selection field choice that should be locked. If present, `field_id` must also be present.", "type": "string"}, "createTime": {"description": "Output only. The time this label lock was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "creator": {"$ref": "GoogleAppsDriveLabelsV2betaUserInfo", "description": "Output only. The user whose credentials were used to create the label lock. Not present if no user was responsible for creating the label lock.", "readOnly": true}, "deleteTime": {"description": "Output only. A timestamp indicating when this label lock was scheduled for deletion. Present only if this label lock is in the `DELETING` state.", "format": "google-datetime", "readOnly": true, "type": "string"}, "fieldId": {"description": "The ID of the field that should be locked. Empty if the whole label should be locked.", "type": "string"}, "name": {"description": "Output only. Resource name of this label lock.", "readOnly": true, "type": "string"}, "state": {"description": "Output only. This label lock's state.", "enum": ["STATE_UNSPECIFIED", "ACTIVE", "DELETING"], "enumDescriptions": ["Unknown state.", "The label lock is active and is being enforced by the server.", "The label lock is being deleted. The label lock will continue to be enforced by the server until it has been fully removed."], "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleAppsDriveLabelsV2betaLabelLockCapabilities": {"description": "A description of a user's capabilities on a label lock.", "id": "GoogleAppsDriveLabelsV2betaLabelLockCapabilities", "properties": {"canViewPolicy": {"description": "True if the user is authorized to view the policy.", "type": "boolean"}}, "type": "object"}, "GoogleAppsDriveLabelsV2betaLabelPermission": {"description": "The permission that applies to a principal (user, group, audience) on a label.", "id": "GoogleAppsDriveLabelsV2betaLabelPermission", "properties": {"audience": {"description": "Audience to grant a role to. The magic value of `audiences/default` may be used to apply the role to the default audience in the context of the organization that owns the label.", "type": "string"}, "email": {"description": "Specifies the email address for a user or group principal. Not populated for audience principals. User and group permissions may only be inserted using an email address. On update requests, if email address is specified, no principal should be specified.", "type": "string"}, "group": {"description": "Group resource name.", "type": "string"}, "name": {"description": "Resource name of this permission.", "type": "string"}, "person": {"description": "Person resource name.", "type": "string"}, "role": {"description": "The role the principal should have.", "enum": ["LABEL_ROLE_UNSPECIFIED", "READER", "APPLIER", "ORGANIZER", "EDITOR"], "enumDescriptions": ["Unknown role.", "A reader can read the label and associated metadata applied to Drive items.", "An applier can write associated metadata on Drive items in which they also have write access to. Implies `READER`.", "An organizer can pin this label in shared drives they manage and add new appliers to the label.", "Editors can make any update including deleting the label which also deletes the associated Drive item metadata. Implies `APPLIER`."], "type": "string"}}, "type": "object"}, "GoogleAppsDriveLabelsV2betaLabelProperties": {"description": "Basic properties of the label.", "id": "GoogleAppsDriveLabelsV2betaLabelProperties", "properties": {"description": {"description": "The description of the label.", "type": "string"}, "title": {"description": "Required. Title of the label.", "type": "string"}}, "type": "object"}, "GoogleAppsDriveLabelsV2betaLabelSchemaCapabilities": {"description": "The capabilities related to this label when editing the label.", "id": "GoogleAppsDriveLabelsV2betaLabelSchemaCapabilities", "properties": {"canDelete": {"description": "Whether the user can delete this label. The user must have permission and the label must be disabled.", "type": "boolean"}, "canDisable": {"description": "Whether the user can disable this label. The user must have permission and this label must not already be disabled.", "type": "boolean"}, "canEnable": {"description": "Whether the user can enable this label. The user must have permission and this label must be disabled.", "type": "boolean"}, "canUpdate": {"description": "Whether the user can change this label.", "type": "boolean"}}, "type": "object"}, "GoogleAppsDriveLabelsV2betaLifecycle": {"description": "The lifecycle state of an object, such as label, field, or choice. For more information, see [Label lifecycle](https://developers.google.com/workspace/drive/labels/guides/label-lifecycle). The lifecycle enforces the following transitions: * `UNPUBLISHED_DRAFT` (starting state) * `UNPUBLISHED_DRAFT` -> `PUBLISHED` * `UNPUBLISHED_DRAFT` -> (Deleted) * `PUBLISHED` -> `DISABLED` * `DISABLED` -> `PUBLISHED` * `DISABLED` -> (Deleted) The published and disabled states have some distinct characteristics: * `Published`: Some kinds of changes might be made to an object in this state, in which case `has_unpublished_changes` will be true. Also, some kinds of changes aren't permitted. Generally, any change that would invalidate or cause new restrictions on existing metadata related to the label are rejected. * `Disabled`: When disabled, the configured `DisabledPolicy` takes effect.", "id": "GoogleAppsDriveLabelsV2betaLifecycle", "properties": {"disabledPolicy": {"$ref": "GoogleAppsDriveLabelsV2betaLifecycleDisabledPolicy", "description": "The policy that governs how to show a disabled label, field, or selection choice."}, "hasUnpublishedChanges": {"description": "Output only. Whether the object associated with this lifecycle has unpublished changes.", "readOnly": true, "type": "boolean"}, "state": {"description": "Output only. The state of the object associated with this lifecycle.", "enum": ["STATE_UNSPECIFIED", "UNPUBLISHED_DRAFT", "PUBLISHED", "DISABLED", "DELETED"], "enumDescriptions": ["Unknown State.", "The initial state of an object. Once published, the object can never return to this state. Once an object is published, certain kinds of changes are no longer permitted.", "The object has been published. The object might have unpublished draft changes as indicated by `has_unpublished_changes`.", "The object has been published and has since been disabled. The object might have unpublished draft changes as indicated by `has_unpublished_changes`.", "The object has been deleted."], "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleAppsDriveLabelsV2betaLifecycleDisabledPolicy": {"description": "The policy that governs how to treat a disabled label, field, or selection choice in different contexts.", "id": "GoogleAppsDriveLabelsV2betaLifecycleDisabledPolicy", "properties": {"hideInSearch": {"description": "Whether to hide this disabled object in the search menu for Drive items. * When `false`, the object is generally shown in the UI as disabled but it appears in the search results when searching for Drive items. * When `true`, the object is generally hidden in the UI when searching for Drive items.", "type": "boolean"}, "showInApply": {"description": "Whether to show this disabled object in the apply menu on Drive items. * When `true`, the object is generally shown in the UI as disabled and is unselectable. * When `false`, the object is generally hidden in the UI.", "type": "boolean"}}, "type": "object"}, "GoogleAppsDriveLabelsV2betaListLabelLocksResponse": {"description": "The response to a `ListLabelLocksRequest`.", "id": "GoogleAppsDriveLabelsV2betaListLabelLocksResponse", "properties": {"labelLocks": {"description": "Label locks.", "items": {"$ref": "GoogleAppsDriveLabelsV2betaLabelLock"}, "type": "array"}, "nextPageToken": {"description": "The token of the next page in the response.", "type": "string"}}, "type": "object"}, "GoogleAppsDriveLabelsV2betaListLabelPermissionsResponse": {"description": "Response for listing the permissions on a label.", "id": "GoogleAppsDriveLabelsV2betaListLabelPermissionsResponse", "properties": {"labelPermissions": {"description": "Label permissions.", "items": {"$ref": "GoogleAppsDriveLabelsV2betaLabelPermission"}, "type": "array"}, "nextPageToken": {"description": "The token of the next page in the response.", "type": "string"}}, "type": "object"}, "GoogleAppsDriveLabelsV2betaListLabelsResponse": {"description": "Response for listing labels.", "id": "GoogleAppsDriveLabelsV2betaListLabelsResponse", "properties": {"labels": {"description": "Labels.", "items": {"$ref": "GoogleAppsDriveLabelsV2betaLabel"}, "type": "array"}, "nextPageToken": {"description": "The token of the next page in the response.", "type": "string"}}, "type": "object"}, "GoogleAppsDriveLabelsV2betaListLimits": {"description": "Limits for list-variant of a field type.", "id": "GoogleAppsDriveLabelsV2betaListLimits", "properties": {"maxEntries": {"description": "Maximum number of values allowed for the field type.", "format": "int32", "type": "integer"}}, "type": "object"}, "GoogleAppsDriveLabelsV2betaLockStatus": {"description": "Contains information about whether a label component should be considered locked.", "id": "GoogleAppsDriveLabelsV2betaLockStatus", "properties": {"locked": {"description": "Output only. Indicates whether this label component is the (direct) target of a label lock. A label component can be implicitly locked even if it's not the direct target of a label lock, in which case this field is set to false.", "readOnly": true, "type": "boolean"}}, "type": "object"}, "GoogleAppsDriveLabelsV2betaLongTextLimits": {"description": "Limits for long text field type.", "id": "GoogleAppsDriveLabelsV2betaLongTextLimits", "properties": {"maxLength": {"description": "Maximum length allowed for a long text field type.", "format": "int32", "type": "integer"}, "minLength": {"description": "Minimum length allowed for a long text field type.", "format": "int32", "type": "integer"}}, "type": "object"}, "GoogleAppsDriveLabelsV2betaPublishLabelRequest": {"description": "Request to publish a label.", "id": "GoogleAppsDriveLabelsV2betaPublishLabelRequest", "properties": {"languageCode": {"description": "The BCP-47 language code to use for evaluating localized field labels. When not specified, values in the default configured language will be used.", "type": "string"}, "useAdminAccess": {"description": "Set to `true` in order to use the user's admin credentials. The server will verify the user is an admin for the label before allowing access.", "type": "boolean"}, "writeControl": {"$ref": "GoogleAppsDriveLabelsV2betaWriteControl", "description": "Provides control over how write requests are executed. Defaults to unset, which means the last write wins."}}, "type": "object"}, "GoogleAppsDriveLabelsV2betaSelectionLimits": {"description": "Limits for selection field type.", "id": "GoogleAppsDriveLabelsV2betaSelectionLimits", "properties": {"listLimits": {"$ref": "GoogleAppsDriveLabelsV2betaListLimits", "description": "Limits for list-variant of a field type."}, "maxChoices": {"description": "Maximum number of choices.", "format": "int32", "type": "integer"}, "maxDeletedChoices": {"description": "Maximum number of deleted choices.", "format": "int32", "type": "integer"}, "maxDisplayNameLength": {"description": "Maximum length for display name.", "format": "int32", "type": "integer"}, "maxIdLength": {"description": "Maximum ID length for a selection option.", "format": "int32", "type": "integer"}}, "type": "object"}, "GoogleAppsDriveLabelsV2betaTextLimits": {"description": "Limits for text field type.", "id": "GoogleAppsDriveLabelsV2betaTextLimits", "properties": {"maxLength": {"description": "Maximum length allowed for a text field type.", "format": "int32", "type": "integer"}, "minLength": {"description": "Minimum length allowed for a text field type.", "format": "int32", "type": "integer"}}, "type": "object"}, "GoogleAppsDriveLabelsV2betaUpdateLabelCopyModeRequest": {"description": "Request to update the `CopyMode` of the given label. Changes to this policy aren't revisioned, don't require publishing, and take effect immediately. \\", "id": "GoogleAppsDriveLabelsV2betaUpdateLabelCopyModeRequest", "properties": {"copyMode": {"description": "Required. Indicates how the applied label and field values should be copied when a Drive item is copied.", "enum": ["COPY_MODE_UNSPECIFIED", "DO_NOT_COPY", "ALWAYS_COPY", "COPY_APPLIABLE"], "enumDescriptions": ["Copy mode unspecified.", "The applied label and field values aren't copied by default when the Drive item it's applied to is copied.", "The applied label and field values are always copied when the Drive item it's applied to is copied. Only admins can use this mode.", "The applied label and field values are copied if the label is appliable by the user making the copy."], "type": "string"}, "languageCode": {"description": "The BCP-47 language code to use for evaluating localized field labels. When not specified, values in the default configured language will be used.", "type": "string"}, "useAdminAccess": {"description": "Set to `true` in order to use the user's admin credentials. The server will verify the user is an admin for the label before allowing access.", "type": "boolean"}, "view": {"description": "When specified, only certain fields belonging to the indicated view will be returned.", "enum": ["LABEL_VIEW_BASIC", "LABEL_VIEW_FULL"], "enumDescriptions": ["Implies the field mask: `name,id,revision_id,label_type,properties.*`", "All possible fields."], "type": "string"}}, "type": "object"}, "GoogleAppsDriveLabelsV2betaUpdateLabelEnabledAppSettingsRequest": {"description": "Request to update the `EnabledAppSettings` of the given label. This change is not revisioned, doesn't require publishing, and takes effect immediately. \\", "id": "GoogleAppsDriveLabelsV2betaUpdateLabelEnabledAppSettingsRequest", "properties": {"enabledAppSettings": {"$ref": "GoogleAppsDriveLabelsV2betaLabelEnabledAppSettings", "description": "Required. The new `EnabledAppSettings` value for the label."}, "languageCode": {"description": "Optional. The BCP-47 language code to use for evaluating localized field labels. When not specified, values in the default configured language will be used.", "type": "string"}, "useAdminAccess": {"description": "Optional. Set to `true` in order to use the user's admin credentials. The server will verify the user is an admin for the label before allowing access.", "type": "boolean"}, "view": {"description": "Optional. When specified, only certain fields belonging to the indicated view will be returned.", "enum": ["LABEL_VIEW_BASIC", "LABEL_VIEW_FULL"], "enumDescriptions": ["Implies the field mask: `name,id,revision_id,label_type,properties.*`", "All possible fields."], "type": "string"}}, "type": "object"}, "GoogleAppsDriveLabelsV2betaUpdateLabelPermissionRequest": {"description": "Updates a label permission. Permissions affect the label resource as a whole, aren't revisioned, and don't require publishing.", "id": "GoogleAppsDriveLabelsV2betaUpdateLabelPermissionRequest", "properties": {"labelPermission": {"$ref": "GoogleAppsDriveLabelsV2betaLabelPermission", "description": "Required. The permission to create or update on the label."}, "parent": {"description": "Required. The parent label resource name.", "type": "string"}, "useAdminAccess": {"description": "Set to `true` in order to use the user's admin credentials. The server will verify the user is an admin for the label before allowing access.", "type": "boolean"}}, "type": "object"}, "GoogleAppsDriveLabelsV2betaUserCapabilities": {"description": "The capabilities of a user.", "id": "GoogleAppsDriveLabelsV2betaUserCapabilities", "properties": {"canAccessLabelManager": {"description": "Output only. Whether the user is allowed access to the label manager.", "readOnly": true, "type": "boolean"}, "canAdministrateLabels": {"description": "Output only. Whether the user is an administrator for the shared labels feature.", "readOnly": true, "type": "boolean"}, "canCreateAdminLabels": {"description": "Output only. Whether the user is allowed to create admin labels.", "readOnly": true, "type": "boolean"}, "canCreateSharedLabels": {"description": "Output only. Whether the user is allowed to create shared labels.", "readOnly": true, "type": "boolean"}, "name": {"description": "Output only. Resource name for the user capabilities.", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleAppsDriveLabelsV2betaUserInfo": {"description": "Information about a user.", "id": "GoogleAppsDriveLabelsV2betaUserInfo", "properties": {"person": {"description": "The identifier for this user that can be used with the [People API](https://developers.google.com/people) to get more information. For example, `people/12345678`.", "type": "string"}}, "type": "object"}, "GoogleAppsDriveLabelsV2betaUserLimits": {"description": "Limits for Field.Type.USER.", "id": "GoogleAppsDriveLabelsV2betaUserLimits", "properties": {"listLimits": {"$ref": "GoogleAppsDriveLabelsV2betaListLimits", "description": "Limits for list-variant of a field type."}}, "type": "object"}, "GoogleAppsDriveLabelsV2betaWriteControl": {"description": "Provides control over how write requests are executed. When not specified, the last write wins.", "id": "GoogleAppsDriveLabelsV2betaWriteControl", "properties": {"requiredRevisionId": {"description": "The revision ID of the label that the write request will be applied to. If this isn't the latest revision of the label, the request will not be processed and will return a 400 Bad Request error.", "type": "string"}}, "type": "object"}, "GoogleProtobufEmpty": {"description": "A generic empty message that you can re-use to avoid defining duplicated empty messages in your APIs. A typical example is to use it as the request or the response type of an API method. For instance: service Foo { rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }", "id": "GoogleProtobufEmpty", "properties": {}, "type": "object"}, "GoogleTypeColor": {"description": "Represents a color in the RGBA color space. This representation is designed for simplicity of conversion to and from color representations in various languages over compactness. For example, the fields of this representation can be trivially provided to the constructor of `java.awt.Color` in Java; it can also be trivially provided to UIColor's `+colorWithRed:green:blue:alpha` method in iOS; and, with just a little work, it can be easily formatted into a CSS `rgba()` string in JavaScript. This reference page doesn't have information about the absolute color space that should be used to interpret the RGB value—for example, sRGB, Adobe RGB, DCI-P3, and BT.2020. By default, applications should assume the sRGB color space. When color equality needs to be decided, implementations, unless documented otherwise, treat two colors as equal if all their red, green, blue, and alpha values each differ by at most `1e-5`. Example (Java): import com.google.type.Color; // ... public static java.awt.Color fromProto(Color protocolor) { float alpha = protocolor.hasAlpha() ? protocolor.getAlpha().getValue() : 1.0; return new java.awt.Color( protocolor.getRed(), protocolor.getGreen(), protocolor.getBlue(), alpha); } public static Color toProto(java.awt.Color color) { float red = (float) color.getRed(); float green = (float) color.getGreen(); float blue = (float) color.getBlue(); float denominator = 255.0; Color.Builder resultBuilder = Color .newBuilder() .setRed(red / denominator) .setGreen(green / denominator) .setBlue(blue / denominator); int alpha = color.getAlpha(); if (alpha != 255) { result.setAlpha( FloatValue .newBuilder() .setValue(((float) alpha) / denominator) .build()); } return resultBuilder.build(); } // ... Example (iOS / Obj-C): // ... static UIColor* fromProto(Color* protocolor) { float red = [protocolor red]; float green = [protocolor green]; float blue = [protocolor blue]; FloatValue* alpha_wrapper = [protocolor alpha]; float alpha = 1.0; if (alpha_wrapper != nil) { alpha = [alpha_wrapper value]; } return [UIColor colorWithRed:red green:green blue:blue alpha:alpha]; } static Color* toProto(UIColor* color) { CGFloat red, green, blue, alpha; if (![color getRed:&red green:&green blue:&blue alpha:&alpha]) { return nil; } Color* result = [[Color alloc] init]; [result setRed:red]; [result setGreen:green]; [result setBlue:blue]; if (alpha <= 0.9999) { [result setAlpha:floatWrapperWithValue(alpha)]; } [result autorelease]; return result; } // ... Example (JavaScript): // ... var protoToCssColor = function(rgb_color) { var redFrac = rgb_color.red || 0.0; var greenFrac = rgb_color.green || 0.0; var blueFrac = rgb_color.blue || 0.0; var red = Math.floor(redFrac * 255); var green = Math.floor(greenFrac * 255); var blue = Math.floor(blueFrac * 255); if (!('alpha' in rgb_color)) { return rgbToCssColor(red, green, blue); } var alphaFrac = rgb_color.alpha.value || 0.0; var rgbParams = [red, green, blue].join(','); return ['rgba(', rgbParams, ',', alphaFrac, ')'].join(''); }; var rgbToCssColor = function(red, green, blue) { var rgbNumber = new Number((red << 16) | (green << 8) | blue); var hexString = rgbNumber.toString(16); var missingZeros = 6 - hexString.length; var resultBuilder = ['#']; for (var i = 0; i < missingZeros; i++) { resultBuilder.push('0'); } resultBuilder.push(hexString); return resultBuilder.join(''); }; // ...", "id": "GoogleTypeColor", "properties": {"alpha": {"description": "The fraction of this color that should be applied to the pixel. That is, the final pixel color is defined by the equation: `pixel color = alpha * (this color) + (1.0 - alpha) * (background color)` This means that a value of 1.0 corresponds to a solid color, whereas a value of 0.0 corresponds to a completely transparent color. This uses a wrapper message rather than a simple float scalar so that it is possible to distinguish between a default value and the value being unset. If omitted, this color object is rendered as a solid color (as if the alpha value had been explicitly given a value of 1.0).", "format": "float", "type": "number"}, "blue": {"description": "The amount of blue in the color as a value in the interval [0, 1].", "format": "float", "type": "number"}, "green": {"description": "The amount of green in the color as a value in the interval [0, 1].", "format": "float", "type": "number"}, "red": {"description": "The amount of red in the color as a value in the interval [0, 1].", "format": "float", "type": "number"}}, "type": "object"}, "GoogleTypeDate": {"description": "Represents a whole or partial calendar date, such as a birthday. The time of day and time zone are either specified elsewhere or are insignificant. The date is relative to the Gregorian Calendar. This can represent one of the following: * A full date, with non-zero year, month, and day values. * A month and day, with a zero year (for example, an anniversary). * A year on its own, with a zero month and a zero day. * A year and month, with a zero day (for example, a credit card expiration date). Related types: * google.type.TimeOfDay * google.type.DateTime * google.protobuf.Timestamp", "id": "GoogleTypeDate", "properties": {"day": {"description": "Day of a month. Must be from 1 to 31 and valid for the year and month, or 0 to specify a year by itself or a year and month where the day isn't significant.", "format": "int32", "type": "integer"}, "month": {"description": "Month of a year. Must be from 1 to 12, or 0 to specify a year without a month and day.", "format": "int32", "type": "integer"}, "year": {"description": "Year of the date. Must be from 1 to 9999, or 0 to specify a date without a year.", "format": "int32", "type": "integer"}}, "type": "object"}}, "servicePath": "", "title": "Drive Labels API", "version": "v2beta", "version_module": true}