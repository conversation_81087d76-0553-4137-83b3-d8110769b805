{"auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/cloud-language": {"description": "Apply machine learning models to reveal the structure and meaning of text"}, "https://www.googleapis.com/auth/cloud-platform": {"description": "See, edit, configure, and delete your Google Cloud data and see the email address for your Google Account."}}}}, "basePath": "", "baseUrl": "https://language.googleapis.com/", "batchPath": "batch", "canonicalName": "Cloud Natural Language", "description": "Provides natural language understanding technologies, such as sentiment analysis, entity recognition, entity sentiment analysis, and other text annotations, to developers.", "discoveryVersion": "v1", "documentationLink": "https://cloud.google.com/natural-language/", "fullyEncodeReservedExpansion": true, "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "language:v1", "kind": "discovery#restDescription", "mtlsRootUrl": "https://language.mtls.googleapis.com/", "name": "language", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"documents": {"methods": {"analyzeEntities": {"description": "Finds named entities (currently proper names and common nouns) in the text along with entity types, salience, mentions for each entity, and other properties.", "flatPath": "v1/documents:analyzeEntities", "httpMethod": "POST", "id": "language.documents.analyzeEntities", "parameterOrder": [], "parameters": {}, "path": "v1/documents:analyzeEntities", "request": {"$ref": "AnalyzeEntitiesRequest"}, "response": {"$ref": "AnalyzeEntitiesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-language", "https://www.googleapis.com/auth/cloud-platform"]}, "analyzeEntitySentiment": {"description": "Finds entities, similar to AnalyzeEntities in the text and analyzes sentiment associated with each entity and its mentions.", "flatPath": "v1/documents:analyzeEntitySentiment", "httpMethod": "POST", "id": "language.documents.analyzeEntitySentiment", "parameterOrder": [], "parameters": {}, "path": "v1/documents:analyzeEntitySentiment", "request": {"$ref": "AnalyzeEntitySentimentRequest"}, "response": {"$ref": "AnalyzeEntitySentimentResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-language", "https://www.googleapis.com/auth/cloud-platform"]}, "analyzeSentiment": {"description": "Analyzes the sentiment of the provided text.", "flatPath": "v1/documents:analyzeSentiment", "httpMethod": "POST", "id": "language.documents.analyzeSentiment", "parameterOrder": [], "parameters": {}, "path": "v1/documents:analyzeSentiment", "request": {"$ref": "AnalyzeSentimentRequest"}, "response": {"$ref": "AnalyzeSentimentResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-language", "https://www.googleapis.com/auth/cloud-platform"]}, "analyzeSyntax": {"description": "Analyzes the syntax of the text and provides sentence boundaries and tokenization along with part of speech tags, dependency trees, and other properties.", "flatPath": "v1/documents:analyzeSyntax", "httpMethod": "POST", "id": "language.documents.analyzeSyntax", "parameterOrder": [], "parameters": {}, "path": "v1/documents:analyzeSyntax", "request": {"$ref": "AnalyzeSyntaxRequest"}, "response": {"$ref": "AnalyzeSyntaxResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-language", "https://www.googleapis.com/auth/cloud-platform"]}, "annotateText": {"description": "A convenience method that provides all the features that analyzeSentiment, analyzeEntities, and analyzeSyntax provide in one call.", "flatPath": "v1/documents:annotateText", "httpMethod": "POST", "id": "language.documents.annotateText", "parameterOrder": [], "parameters": {}, "path": "v1/documents:annotateText", "request": {"$ref": "AnnotateTextRequest"}, "response": {"$ref": "AnnotateTextResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-language", "https://www.googleapis.com/auth/cloud-platform"]}, "classifyText": {"description": "Classifies a document into categories.", "flatPath": "v1/documents:classifyText", "httpMethod": "POST", "id": "language.documents.classifyText", "parameterOrder": [], "parameters": {}, "path": "v1/documents:classifyText", "request": {"$ref": "ClassifyTextRequest"}, "response": {"$ref": "ClassifyTextResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-language", "https://www.googleapis.com/auth/cloud-platform"]}, "moderateText": {"description": "Moderates a document for harmful and sensitive categories.", "flatPath": "v1/documents:moderateText", "httpMethod": "POST", "id": "language.documents.moderateText", "parameterOrder": [], "parameters": {}, "path": "v1/documents:moderateText", "request": {"$ref": "ModerateTextRequest"}, "response": {"$ref": "ModerateTextResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-language", "https://www.googleapis.com/auth/cloud-platform"]}}}}, "revision": "20250518", "rootUrl": "https://language.googleapis.com/", "schemas": {"AnalyzeEntitiesRequest": {"description": "The entity analysis request message.", "id": "AnalyzeEntitiesRequest", "properties": {"document": {"$ref": "Document", "description": "Required. Input document."}, "encodingType": {"description": "The encoding type used by the API to calculate offsets.", "enum": ["NONE", "UTF8", "UTF16", "UTF32"], "enumDescriptions": ["If `EncodingType` is not specified, encoding-dependent information (such as `begin_offset`) will be set at `-1`.", "Encoding-dependent information (such as `begin_offset`) is calculated based on the UTF-8 encoding of the input. C++ and Go are examples of languages that use this encoding natively.", "Encoding-dependent information (such as `begin_offset`) is calculated based on the UTF-16 encoding of the input. Java and JavaScript are examples of languages that use this encoding natively.", "Encoding-dependent information (such as `begin_offset`) is calculated based on the UTF-32 encoding of the input. Python is an example of a language that uses this encoding natively."], "type": "string"}}, "type": "object"}, "AnalyzeEntitiesResponse": {"description": "The entity analysis response message.", "id": "AnalyzeEntitiesResponse", "properties": {"entities": {"description": "The recognized entities in the input document.", "items": {"$ref": "Entity"}, "type": "array"}, "language": {"description": "The language of the text, which will be the same as the language specified in the request or, if not specified, the automatically-detected language. See Document.language field for more details.", "type": "string"}}, "type": "object"}, "AnalyzeEntitySentimentRequest": {"description": "The entity-level sentiment analysis request message.", "id": "AnalyzeEntitySentimentRequest", "properties": {"document": {"$ref": "Document", "description": "Required. Input document."}, "encodingType": {"description": "The encoding type used by the API to calculate offsets.", "enum": ["NONE", "UTF8", "UTF16", "UTF32"], "enumDescriptions": ["If `EncodingType` is not specified, encoding-dependent information (such as `begin_offset`) will be set at `-1`.", "Encoding-dependent information (such as `begin_offset`) is calculated based on the UTF-8 encoding of the input. C++ and Go are examples of languages that use this encoding natively.", "Encoding-dependent information (such as `begin_offset`) is calculated based on the UTF-16 encoding of the input. Java and JavaScript are examples of languages that use this encoding natively.", "Encoding-dependent information (such as `begin_offset`) is calculated based on the UTF-32 encoding of the input. Python is an example of a language that uses this encoding natively."], "type": "string"}}, "type": "object"}, "AnalyzeEntitySentimentResponse": {"description": "The entity-level sentiment analysis response message.", "id": "AnalyzeEntitySentimentResponse", "properties": {"entities": {"description": "The recognized entities in the input document with associated sentiments.", "items": {"$ref": "Entity"}, "type": "array"}, "language": {"description": "The language of the text, which will be the same as the language specified in the request or, if not specified, the automatically-detected language. See Document.language field for more details.", "type": "string"}}, "type": "object"}, "AnalyzeSentimentRequest": {"description": "The sentiment analysis request message.", "id": "AnalyzeSentimentRequest", "properties": {"document": {"$ref": "Document", "description": "Required. Input document."}, "encodingType": {"description": "The encoding type used by the API to calculate sentence offsets.", "enum": ["NONE", "UTF8", "UTF16", "UTF32"], "enumDescriptions": ["If `EncodingType` is not specified, encoding-dependent information (such as `begin_offset`) will be set at `-1`.", "Encoding-dependent information (such as `begin_offset`) is calculated based on the UTF-8 encoding of the input. C++ and Go are examples of languages that use this encoding natively.", "Encoding-dependent information (such as `begin_offset`) is calculated based on the UTF-16 encoding of the input. Java and JavaScript are examples of languages that use this encoding natively.", "Encoding-dependent information (such as `begin_offset`) is calculated based on the UTF-32 encoding of the input. Python is an example of a language that uses this encoding natively."], "type": "string"}}, "type": "object"}, "AnalyzeSentimentResponse": {"description": "The sentiment analysis response message.", "id": "AnalyzeSentimentResponse", "properties": {"documentSentiment": {"$ref": "Sentiment", "description": "The overall sentiment of the input document."}, "language": {"description": "The language of the text, which will be the same as the language specified in the request or, if not specified, the automatically-detected language. See Document.language field for more details.", "type": "string"}, "sentences": {"description": "The sentiment for all the sentences in the document.", "items": {"$ref": "Sentence"}, "type": "array"}}, "type": "object"}, "AnalyzeSyntaxRequest": {"description": "The syntax analysis request message.", "id": "AnalyzeSyntaxRequest", "properties": {"document": {"$ref": "Document", "description": "Required. Input document."}, "encodingType": {"description": "The encoding type used by the API to calculate offsets.", "enum": ["NONE", "UTF8", "UTF16", "UTF32"], "enumDescriptions": ["If `EncodingType` is not specified, encoding-dependent information (such as `begin_offset`) will be set at `-1`.", "Encoding-dependent information (such as `begin_offset`) is calculated based on the UTF-8 encoding of the input. C++ and Go are examples of languages that use this encoding natively.", "Encoding-dependent information (such as `begin_offset`) is calculated based on the UTF-16 encoding of the input. Java and JavaScript are examples of languages that use this encoding natively.", "Encoding-dependent information (such as `begin_offset`) is calculated based on the UTF-32 encoding of the input. Python is an example of a language that uses this encoding natively."], "type": "string"}}, "type": "object"}, "AnalyzeSyntaxResponse": {"description": "The syntax analysis response message.", "id": "AnalyzeSyntaxResponse", "properties": {"language": {"description": "The language of the text, which will be the same as the language specified in the request or, if not specified, the automatically-detected language. See Document.language field for more details.", "type": "string"}, "sentences": {"description": "Sentences in the input document.", "items": {"$ref": "Sentence"}, "type": "array"}, "tokens": {"description": "Tokens, along with their syntactic information, in the input document.", "items": {"$ref": "Token"}, "type": "array"}}, "type": "object"}, "AnnotateTextRequest": {"description": "The request message for the text annotation API, which can perform multiple analysis types (sentiment, entities, and syntax) in one call.", "id": "AnnotateTextRequest", "properties": {"document": {"$ref": "Document", "description": "Required. Input document."}, "encodingType": {"description": "The encoding type used by the API to calculate offsets.", "enum": ["NONE", "UTF8", "UTF16", "UTF32"], "enumDescriptions": ["If `EncodingType` is not specified, encoding-dependent information (such as `begin_offset`) will be set at `-1`.", "Encoding-dependent information (such as `begin_offset`) is calculated based on the UTF-8 encoding of the input. C++ and Go are examples of languages that use this encoding natively.", "Encoding-dependent information (such as `begin_offset`) is calculated based on the UTF-16 encoding of the input. Java and JavaScript are examples of languages that use this encoding natively.", "Encoding-dependent information (such as `begin_offset`) is calculated based on the UTF-32 encoding of the input. Python is an example of a language that uses this encoding natively."], "type": "string"}, "features": {"$ref": "AnnotateTextRequestFeatures", "description": "Required. The enabled features."}}, "type": "object"}, "AnnotateTextRequestFeatures": {"description": "All available features for sentiment, syntax, and semantic analysis. Setting each one to true will enable that specific analysis for the input.", "id": "AnnotateTextRequestFeatures", "properties": {"classificationModelOptions": {"$ref": "ClassificationModelOptions", "description": "Optional. The model options to use for classification. Defaults to v1 options if not specified. Only used if `classify_text` is set to true."}, "classifyText": {"description": "Classify the full document into categories.", "type": "boolean"}, "extractDocumentSentiment": {"description": "Extract document-level sentiment.", "type": "boolean"}, "extractEntities": {"description": "Extract entities.", "type": "boolean"}, "extractEntitySentiment": {"description": "Extract entities and their associated sentiment.", "type": "boolean"}, "extractSyntax": {"description": "Extract syntax information.", "type": "boolean"}, "moderateText": {"description": "Moderate the document for harmful and sensitive categories.", "type": "boolean"}}, "type": "object"}, "AnnotateTextResponse": {"description": "The text annotations response message.", "id": "AnnotateTextResponse", "properties": {"categories": {"description": "Categories identified in the input document.", "items": {"$ref": "ClassificationCategory"}, "type": "array"}, "documentSentiment": {"$ref": "Sentiment", "description": "The overall sentiment for the document. Populated if the user enables AnnotateTextRequest.Features.extract_document_sentiment."}, "entities": {"description": "Entities, along with their semantic information, in the input document. Populated if the user enables AnnotateTextRequest.Features.extract_entities.", "items": {"$ref": "Entity"}, "type": "array"}, "language": {"description": "The language of the text, which will be the same as the language specified in the request or, if not specified, the automatically-detected language. See Document.language field for more details.", "type": "string"}, "moderationCategories": {"description": "Harmful and sensitive categories identified in the input document.", "items": {"$ref": "ClassificationCategory"}, "type": "array"}, "sentences": {"description": "Sentences in the input document. Populated if the user enables AnnotateTextRequest.Features.extract_syntax.", "items": {"$ref": "Sentence"}, "type": "array"}, "tokens": {"description": "Tokens, along with their syntactic information, in the input document. Populated if the user enables AnnotateTextRequest.Features.extract_syntax.", "items": {"$ref": "Token"}, "type": "array"}}, "type": "object"}, "ClassificationCategory": {"description": "Represents a category returned from the text classifier.", "id": "ClassificationCategory", "properties": {"confidence": {"description": "The classifier's confidence of the category. Number represents how certain the classifier is that this category represents the given text.", "format": "float", "type": "number"}, "name": {"description": "The name of the category representing the document.", "type": "string"}}, "type": "object"}, "ClassificationModelOptions": {"description": "Model options available for classification requests.", "id": "ClassificationModelOptions", "properties": {"v1Model": {"$ref": "ClassificationModelOptionsV1Model", "description": "Setting this field will use the V1 model and V1 content categories version. The V1 model is a legacy model; support for this will be discontinued in the future."}, "v2Model": {"$ref": "ClassificationModelOptionsV2Model", "description": "Setting this field will use the V2 model with the appropriate content categories version. The V2 model is a better performing model."}}, "type": "object"}, "ClassificationModelOptionsV1Model": {"description": "Options for the V1 model.", "id": "ClassificationModelOptionsV1Model", "properties": {}, "type": "object"}, "ClassificationModelOptionsV2Model": {"description": "Options for the V2 model.", "id": "ClassificationModelOptionsV2Model", "properties": {"contentCategoriesVersion": {"description": "The content categories used for classification.", "enum": ["CONTENT_CATEGORIES_VERSION_UNSPECIFIED", "V1", "V2"], "enumDescriptions": ["If `ContentCategoriesVersion` is not specified, this option will default to `V1`.", "Legacy content categories of our initial launch in 2017.", "Updated content categories in 2022."], "type": "string"}}, "type": "object"}, "ClassifyTextRequest": {"description": "The document classification request message.", "id": "ClassifyTextRequest", "properties": {"classificationModelOptions": {"$ref": "ClassificationModelOptions", "description": "Optional. Model options to use for classification. Defaults to v1 options if not specified."}, "document": {"$ref": "Document", "description": "Required. Input document."}}, "type": "object"}, "ClassifyTextResponse": {"description": "The document classification response message.", "id": "ClassifyTextResponse", "properties": {"categories": {"description": "Categories representing the input document.", "items": {"$ref": "ClassificationCategory"}, "type": "array"}}, "type": "object"}, "Color": {"description": "Represents a color in the RGBA color space. This representation is designed for simplicity of conversion to and from color representations in various languages over compactness. For example, the fields of this representation can be trivially provided to the constructor of `java.awt.Color` in Java; it can also be trivially provided to UIColor's `+colorWithRed:green:blue:alpha` method in iOS; and, with just a little work, it can be easily formatted into a CSS `rgba()` string in JavaScript. This reference page doesn't have information about the absolute color space that should be used to interpret the RGB value—for example, sRGB, Adobe RGB, DCI-P3, and BT.2020. By default, applications should assume the sRGB color space. When color equality needs to be decided, implementations, unless documented otherwise, treat two colors as equal if all their red, green, blue, and alpha values each differ by at most `1e-5`. Example (Java): import com.google.type.Color; // ... public static java.awt.Color fromProto(Color protocolor) { float alpha = protocolor.hasAlpha() ? protocolor.getAlpha().getValue() : 1.0; return new java.awt.Color( protocolor.getRed(), protocolor.getGreen(), protocolor.getBlue(), alpha); } public static Color toProto(java.awt.Color color) { float red = (float) color.getRed(); float green = (float) color.getGreen(); float blue = (float) color.getBlue(); float denominator = 255.0; Color.Builder resultBuilder = Color .newBuilder() .setRed(red / denominator) .setGreen(green / denominator) .setBlue(blue / denominator); int alpha = color.getAlpha(); if (alpha != 255) { result.setAlpha( FloatValue .newBuilder() .setValue(((float) alpha) / denominator) .build()); } return resultBuilder.build(); } // ... Example (iOS / Obj-C): // ... static UIColor* fromProto(Color* protocolor) { float red = [protocolor red]; float green = [protocolor green]; float blue = [protocolor blue]; FloatValue* alpha_wrapper = [protocolor alpha]; float alpha = 1.0; if (alpha_wrapper != nil) { alpha = [alpha_wrapper value]; } return [UIColor colorWithRed:red green:green blue:blue alpha:alpha]; } static Color* toProto(UIColor* color) { CGFloat red, green, blue, alpha; if (![color getRed:&red green:&green blue:&blue alpha:&alpha]) { return nil; } Color* result = [[Color alloc] init]; [result setRed:red]; [result setGreen:green]; [result setBlue:blue]; if (alpha <= 0.9999) { [result setAlpha:floatWrapperWithValue(alpha)]; } [result autorelease]; return result; } // ... Example (JavaScript): // ... var protoToCssColor = function(rgb_color) { var redFrac = rgb_color.red || 0.0; var greenFrac = rgb_color.green || 0.0; var blueFrac = rgb_color.blue || 0.0; var red = Math.floor(redFrac * 255); var green = Math.floor(greenFrac * 255); var blue = Math.floor(blueFrac * 255); if (!('alpha' in rgb_color)) { return rgbToCssColor(red, green, blue); } var alphaFrac = rgb_color.alpha.value || 0.0; var rgbParams = [red, green, blue].join(','); return ['rgba(', rgbParams, ',', alphaFrac, ')'].join(''); }; var rgbToCssColor = function(red, green, blue) { var rgbNumber = new Number((red << 16) | (green << 8) | blue); var hexString = rgbNumber.toString(16); var missingZeros = 6 - hexString.length; var resultBuilder = ['#']; for (var i = 0; i < missingZeros; i++) { resultBuilder.push('0'); } resultBuilder.push(hexString); return resultBuilder.join(''); }; // ...", "id": "Color", "properties": {"alpha": {"description": "The fraction of this color that should be applied to the pixel. That is, the final pixel color is defined by the equation: `pixel color = alpha * (this color) + (1.0 - alpha) * (background color)` This means that a value of 1.0 corresponds to a solid color, whereas a value of 0.0 corresponds to a completely transparent color. This uses a wrapper message rather than a simple float scalar so that it is possible to distinguish between a default value and the value being unset. If omitted, this color object is rendered as a solid color (as if the alpha value had been explicitly given a value of 1.0).", "format": "float", "type": "number"}, "blue": {"description": "The amount of blue in the color as a value in the interval [0, 1].", "format": "float", "type": "number"}, "green": {"description": "The amount of green in the color as a value in the interval [0, 1].", "format": "float", "type": "number"}, "red": {"description": "The amount of red in the color as a value in the interval [0, 1].", "format": "float", "type": "number"}}, "type": "object"}, "CpuMetric": {"description": "Metric for billing reports.", "id": "CpuMetric", "properties": {"coreNumber": {"description": "Required. Number of CPU cores.", "format": "int64", "type": "string"}, "coreSec": {"description": "Required. Total seconds of core usage, e.g. 4.", "format": "int64", "type": "string"}, "cpuType": {"description": "Required. Type of cpu, e.g. N2.", "enum": ["UNKNOWN_CPU_TYPE", "A2", "A3", "A4", "A4X", "C2", "C2D", "CUSTOM", "E2", "G2", "C3", "M2", "M1", "N1", "N2_CUSTOM", "N2", "N2D"], "enumDescriptions": ["", "GPU-based machine, skip quota reporting.", "GPU-based machine, skip quota reporting.", "GPU-based machine, skip quota reporting.", "GPU-based machine, skip quota reporting.", "COMPUTE_OPTIMIZED", "", "", "", "GPU-based machine, skip quota reporting.", "", "MEMORY_OPTIMIZED_UPGRADE_PREMIUM", "MEMORY_OPTIMIZED", "", "", "", ""], "type": "string"}, "machineSpec": {"description": "Required. Machine spec, e.g. N1_STANDARD_4.", "enum": ["UNKNOWN_MACHINE_SPEC", "N1_STANDARD_2", "N1_STANDARD_4", "N1_STANDARD_8", "N1_STANDARD_16", "N1_STANDARD_32", "N1_STANDARD_64", "N1_STANDARD_96", "N1_HIGHMEM_2", "N1_HIGHMEM_4", "N1_HIGHMEM_8", "N1_HIGHMEM_16", "N1_HIGHMEM_32", "N1_HIGHMEM_64", "N1_HIGHMEM_96", "N1_HIGHCPU_2", "N1_HIGHCPU_4", "N1_HIGHCPU_8", "N1_HIGHCPU_16", "N1_HIGHCPU_32", "N1_HIGHCPU_64", "N1_HIGHCPU_96", "A2_HIGHGPU_1G", "A2_HIGHGPU_2G", "A2_HIGHGPU_4G", "A2_HIGHGPU_8G", "A2_MEGAGPU_16G", "A2_ULTRAGPU_1G", "A2_ULTRAGPU_2G", "A2_ULTRAGPU_4G", "A2_ULTRAGPU_8G", "A3_HIGHGPU_1G", "A3_HIGHGPU_2G", "A3_HIGHGPU_4G", "A3_HIGHGPU_8G", "A3_MEGAGPU_8G", "A3_ULTRAGPU_8G", "A3_EDGEGPU_8G", "A4_HIGHGPU_8G", "A4X_HIGHGPU_4G", "E2_STANDARD_2", "E2_STANDARD_4", "E2_STANDARD_8", "E2_STANDARD_16", "E2_STANDARD_32", "E2_HIGHMEM_2", "E2_HIGHMEM_4", "E2_HIGHMEM_8", "E2_HIGHMEM_16", "E2_HIGHCPU_2", "E2_HIGHCPU_4", "E2_HIGHCPU_8", "E2_HIGHCPU_16", "E2_HIGHCPU_32", "N2_STANDARD_2", "N2_STANDARD_4", "N2_STANDARD_8", "N2_STANDARD_16", "N2_STANDARD_32", "N2_STANDARD_48", "N2_STANDARD_64", "N2_STANDARD_80", "N2_STANDARD_96", "N2_STANDARD_128", "N2_HIGHMEM_2", "N2_HIGHMEM_4", "N2_HIGHMEM_8", "N2_HIGHMEM_16", "N2_HIGHMEM_32", "N2_HIGHMEM_48", "N2_HIGHMEM_64", "N2_HIGHMEM_80", "N2_HIGHMEM_96", "N2_HIGHMEM_128", "N2_HIGHCPU_2", "N2_HIGHCPU_4", "N2_HIGHCPU_8", "N2_HIGHCPU_16", "N2_HIGHCPU_32", "N2_HIGHCPU_48", "N2_HIGHCPU_64", "N2_HIGHCPU_80", "N2_HIGHCPU_96", "N2D_STANDARD_2", "N2D_STANDARD_4", "N2D_STANDARD_8", "N2D_STANDARD_16", "N2D_STANDARD_32", "N2D_STANDARD_48", "N2D_STANDARD_64", "N2D_STANDARD_80", "N2D_STANDARD_96", "N2D_STANDARD_128", "N2D_STANDARD_224", "N2D_HIGHMEM_2", "N2D_HIGHMEM_4", "N2D_HIGHMEM_8", "N2D_HIGHMEM_16", "N2D_HIGHMEM_32", "N2D_HIGHMEM_48", "N2D_HIGHMEM_64", "N2D_HIGHMEM_80", "N2D_HIGHMEM_96", "N2D_HIGHCPU_2", "N2D_HIGHCPU_4", "N2D_HIGHCPU_8", "N2D_HIGHCPU_16", "N2D_HIGHCPU_32", "N2D_HIGHCPU_48", "N2D_HIGHCPU_64", "N2D_HIGHCPU_80", "N2D_HIGHCPU_96", "N2D_HIGHCPU_128", "N2D_HIGHCPU_224", "C2_STANDARD_4", "C2_STANDARD_8", "C2_STANDARD_16", "C2_STANDARD_30", "C2_STANDARD_60", "C2D_STANDARD_2", "C2D_STANDARD_4", "C2D_STANDARD_8", "C2D_STANDARD_16", "C2D_STANDARD_32", "C2D_STANDARD_56", "C2D_STANDARD_112", "C2D_HIGHCPU_2", "C2D_HIGHCPU_4", "C2D_HIGHCPU_8", "C2D_HIGHCPU_16", "C2D_HIGHCPU_32", "C2D_HIGHCPU_56", "C2D_HIGHCPU_112", "C2D_HIGHMEM_2", "C2D_HIGHMEM_4", "C2D_HIGHMEM_8", "C2D_HIGHMEM_16", "C2D_HIGHMEM_32", "C2D_HIGHMEM_56", "C2D_HIGHMEM_112", "G2_STANDARD_4", "G2_STANDARD_8", "G2_STANDARD_12", "G2_STANDARD_16", "G2_STANDARD_24", "G2_STANDARD_32", "G2_STANDARD_48", "G2_STANDARD_96", "C3_STANDARD_4", "C3_STANDARD_8", "C3_STANDARD_22", "C3_STANDARD_44", "C3_STANDARD_88", "C3_STANDARD_176", "C3_HIGHCPU_4", "C3_HIGHCPU_8", "C3_HIGHCPU_22", "C3_HIGHCPU_44", "C3_HIGHCPU_88", "C3_HIGHCPU_176", "C3_HIGHMEM_4", "C3_HIGHMEM_8", "C3_HIGHMEM_22", "C3_HIGHMEM_44", "C3_HIGHMEM_88", "C3_HIGHMEM_176"], "enumDescriptions": ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], "type": "string"}, "trackingLabels": {"additionalProperties": {"type": "string"}, "description": "Billing tracking labels. They do not contain any user data but only the labels set by Vertex Core Infra itself. Tracking labels' keys are defined with special format: goog-[\\p{Ll}\\p{N}]+ E.g. \"key\": \"goog-k8s-cluster-name\",\"value\": \"us-east1-b4rk\"", "type": "object"}}, "type": "object"}, "DependencyEdge": {"description": "Represents dependency parse tree information for a token. (For more information on dependency labels, see http://www.aclweb.org/anthology/P13-2017", "id": "DependencyEdge", "properties": {"headTokenIndex": {"description": "Represents the head of this token in the dependency tree. This is the index of the token which has an arc going to this token. The index is the position of the token in the array of tokens returned by the API method. If this token is a root token, then the `head_token_index` is its own index.", "format": "int32", "type": "integer"}, "label": {"description": "The parse label for the token.", "enum": ["UNKNOWN", "ABBREV", "ACOMP", "ADVCL", "ADVMOD", "AMOD", "APPOS", "ATTR", "AUX", "AUXPASS", "CC", "CCOMP", "CONJ", "CSUBJ", "CSUBJPASS", "DEP", "DET", "DISCOURSE", "DOBJ", "EXPL", "GOESWITH", "IOBJ", "MARK", "MWE", "MWV", "NEG", "NN", "NPADVMOD", "NSUBJ", "NSUBJPASS", "NUM", "NUMBER", "P", "PARATAXIS", "PARTMOD", "PCOMP", "POBJ", "POSS", "POSTNEG", "PRECOMP", "PRECONJ", "PREDET", "PREF", "PREP", "PRONL", "PRT", "PS", "QUANTMOD", "RCMOD", "RCMODREL", "RDROP", "REF", "REMNANT", "REPARANDUM", "ROOT", "SNUM", "SUFF", "TMOD", "TOPIC", "VMOD", "VOCATIVE", "XCOMP", "SUFFIX", "TITLE", "ADVPHMOD", "AUXCAUS", "AUXVV", "DTMOD", "FOREIGN", "KW", "LIST", "NOMC", "NOMCSUBJ", "NOMCSUBJPASS", "NUMC", "COP", "DISLOCATED", "ASP", "GMOD", "GOBJ", "INFMOD", "MES", "NCOMP"], "enumDescriptions": ["Unknown", "Abbreviation modifier", "Adjectival complement", "Adverbial clause modifier", "Adverbial modifier", "Adjectival modifier of an NP", "Appositional modifier of an NP", "Attribute dependent of a copular verb", "Auxiliary (non-main) verb", "Passive auxiliary", "Coordinating conjunction", "Clausal complement of a verb or adjective", "Conjunct", "Clausal subject", "Clausal passive subject", "Dependency (unable to determine)", "Determiner", "Discourse", "Direct object", "Expletive", "Goes with (part of a word in a text not well edited)", "Indirect object", "Marker (word introducing a subordinate clause)", "Multi-word expression", "Multi-word verbal expression", "Negation modifier", "Noun compound modifier", "Noun phrase used as an adverbial modifier", "Nominal subject", "Passive nominal subject", "Numeric modifier of a noun", "Element of compound number", "Punctuation mark", "Parataxis relation", "Participial modifier", "The complement of a preposition is a clause", "Object of a preposition", "Possession modifier", "Postverbal negative particle", "Predicate complement", "Preconjunt", "Predeterminer", "Prefix", "Prepositional modifier", "The relationship between a verb and verbal morpheme", "Particle", "Associative or possessive marker", "Quantifier phrase modifier", "Relative clause modifier", "Complementizer in relative clause", "Ellipsis without a preceding predicate", "Referent", "<PERSON><PERSON><PERSON>", "Reparandum", "Root", "Suffix specifying a unit of number", "Suffix", "Temporal modifier", "Topic marker", "Clause headed by an infinite form of the verb that modifies a noun", "Vocative", "Open clausal complement", "Name suffix", "Name title", "Adverbial phrase modifier", "Causative auxiliary", "Helper auxiliary", "<PERSON><PERSON><PERSON> (Prenominal modifier)", "Foreign words", "Keyword", "List for chains of comparable items", "Nominalized clause", "Nominalized clausal subject", "Nominalized clausal passive", "Compound of numeric modifier", "<PERSON><PERSON><PERSON>", "Dislocated relation (for fronted/topicalized elements)", "Aspect marker", "Genitive modifier", "Genitive object", "Infinitival modifier", "Measure", "Nominal complement of a noun"], "type": "string"}}, "type": "object"}, "DiskMetric": {"id": "DiskMetric", "properties": {"diskType": {"description": "Required. Type of Disk, e.g. REGIONAL_SSD.", "enum": ["UNKNOWN_DISK_TYPE", "REGIONAL_SSD", "REGIONAL_STORAGE", "PD_SSD", "PD_STANDARD", "STORAGE_SNAPSHOT"], "enumDescriptions": ["", "", "", "", "", ""], "type": "string"}, "gibSec": {"description": "Required. Seconds of physical disk usage, e.g. 3600.", "format": "int64", "type": "string"}}, "type": "object"}, "Document": {"description": "Represents the input to API methods.", "id": "Document", "properties": {"content": {"description": "The content of the input in string format. Cloud audit logging exempt since it is based on user data.", "type": "string"}, "gcsContentUri": {"description": "The Google Cloud Storage URI where the file content is located. This URI must be of the form: gs://bucket_name/object_name. For more details, see https://cloud.google.com/storage/docs/reference-uris. NOTE: Cloud Storage object versioning is not supported.", "type": "string"}, "language": {"description": "The language of the document (if not specified, the language is automatically detected). Both ISO and BCP-47 language codes are accepted. [Language Support](https://cloud.google.com/natural-language/docs/languages) lists currently supported languages for each API method. If the language (either specified by the caller or automatically detected) is not supported by the called API method, an `INVALID_ARGUMENT` error is returned.", "type": "string"}, "type": {"description": "Required. If the type is not set or is `TYPE_UNSPECIFIED`, returns an `INVALID_ARGUMENT` error.", "enum": ["TYPE_UNSPECIFIED", "PLAIN_TEXT", "HTML"], "enumDescriptions": ["The content type is not specified.", "Plain text", "HTML"], "type": "string"}}, "type": "object"}, "Entity": {"description": "Represents a phrase in the text that is a known entity, such as a person, an organization, or location. The API associates information, such as salience and mentions, with entities.", "id": "Entity", "properties": {"mentions": {"description": "The mentions of this entity in the input document. The API currently supports proper noun mentions.", "items": {"$ref": "EntityMention"}, "type": "array"}, "metadata": {"additionalProperties": {"type": "string"}, "description": "Metadata associated with the entity. For most entity types, the metadata is a Wikipedia URL (`wikipedia_url`) and Knowledge Graph MID (`mid`), if they are available. For the metadata associated with other entity types, see the Type table below.", "type": "object"}, "name": {"description": "The representative name for the entity.", "type": "string"}, "salience": {"description": "The salience score associated with the entity in the [0, 1.0] range. The salience score for an entity provides information about the importance or centrality of that entity to the entire document text. Scores closer to 0 are less salient, while scores closer to 1.0 are highly salient.", "format": "float", "type": "number"}, "sentiment": {"$ref": "Sentiment", "description": "For calls to AnalyzeEntitySentiment or if AnnotateTextRequest.Features.extract_entity_sentiment is set to true, this field will contain the aggregate sentiment expressed for this entity in the provided document."}, "type": {"description": "The entity type.", "enum": ["UNKNOWN", "PERSON", "LOCATION", "ORGANIZATION", "EVENT", "WORK_OF_ART", "CONSUMER_GOOD", "OTHER", "PHONE_NUMBER", "ADDRESS", "DATE", "NUMBER", "PRICE"], "enumDescriptions": ["Unknown", "Person", "Location", "Organization", "Event", "Artwork", "Consumer product", "Other types of entities", "Phone number The metadata lists the phone number, formatted according to local convention, plus whichever additional elements appear in the text: * `number` - the actual number, broken down into sections as per local convention * `national_prefix` - country code, if detected * `area_code` - region or area code, if detected * `extension` - phone extension (to be dialed after connection), if detected", "Address The metadata identifies the street number and locality plus whichever additional elements appear in the text: * `street_number` - street number * `locality` - city or town * `street_name` - street/route name, if detected * `postal_code` - postal code, if detected * `country` - country, if detected< * `broad_region` - administrative area, such as the state, if detected * `narrow_region` - smaller administrative area, such as county, if detected * `sublocality` - used in Asian addresses to demark a district within a city, if detected", "Date The metadata identifies the components of the date: * `year` - four digit year, if detected * `month` - two digit month number, if detected * `day` - two digit day number, if detected", "Number The metadata is the number itself.", "Price The metadata identifies the `value` and `currency`."], "type": "string"}}, "type": "object"}, "EntityMention": {"description": "Represents a mention for an entity in the text. Currently, proper noun mentions are supported.", "id": "EntityMention", "properties": {"sentiment": {"$ref": "Sentiment", "description": "For calls to AnalyzeEntitySentiment or if AnnotateTextRequest.Features.extract_entity_sentiment is set to true, this field will contain the sentiment expressed for this mention of the entity in the provided document."}, "text": {"$ref": "TextSpan", "description": "The mention text."}, "type": {"description": "The type of the entity mention.", "enum": ["TYPE_UNKNOWN", "PROPER", "COMMON"], "enumDescriptions": ["Unknown", "Proper name", "Common noun (or noun compound)"], "type": "string"}}, "type": "object"}, "GpuMetric": {"id": "GpuMetric", "properties": {"gpuSec": {"description": "Required. Seconds of GPU usage, e.g. 3600.", "format": "int64", "type": "string"}, "gpuType": {"description": "Required. Type of GPU, e.g. NVIDIA_TESLA_V100.", "enum": ["UNKNOWN_GPU_TYPE", "NVIDIA_TESLA_A100", "NVIDIA_A100_80GB", "NVIDIA_B200", "NVIDIA_GB200", "NVIDIA_TESLA_K80", "NVIDIA_L4", "NVIDIA_TESLA_P100", "NVIDIA_TESLA_P4", "NVIDIA_TESLA_T4", "NVIDIA_TESLA_V100", "NVIDIA_H100_80GB", "NVIDIA_H100_MEGA_80GB", "NVIDIA_H200_141GB"], "enumDescriptions": ["", "", "", "", "", "", "", "", "", "", "", "", "", ""], "type": "string"}, "machineSpec": {"description": "Required. Machine spec, e.g. N1_STANDARD_4.", "enum": ["UNKNOWN_MACHINE_SPEC", "N1_STANDARD_2", "N1_STANDARD_4", "N1_STANDARD_8", "N1_STANDARD_16", "N1_STANDARD_32", "N1_STANDARD_64", "N1_STANDARD_96", "N1_HIGHMEM_2", "N1_HIGHMEM_4", "N1_HIGHMEM_8", "N1_HIGHMEM_16", "N1_HIGHMEM_32", "N1_HIGHMEM_64", "N1_HIGHMEM_96", "N1_HIGHCPU_2", "N1_HIGHCPU_4", "N1_HIGHCPU_8", "N1_HIGHCPU_16", "N1_HIGHCPU_32", "N1_HIGHCPU_64", "N1_HIGHCPU_96", "A2_HIGHGPU_1G", "A2_HIGHGPU_2G", "A2_HIGHGPU_4G", "A2_HIGHGPU_8G", "A2_MEGAGPU_16G", "A2_ULTRAGPU_1G", "A2_ULTRAGPU_2G", "A2_ULTRAGPU_4G", "A2_ULTRAGPU_8G", "A3_HIGHGPU_1G", "A3_HIGHGPU_2G", "A3_HIGHGPU_4G", "A3_HIGHGPU_8G", "A3_MEGAGPU_8G", "A3_ULTRAGPU_8G", "A3_EDGEGPU_8G", "A4_HIGHGPU_8G", "A4X_HIGHGPU_4G", "E2_STANDARD_2", "E2_STANDARD_4", "E2_STANDARD_8", "E2_STANDARD_16", "E2_STANDARD_32", "E2_HIGHMEM_2", "E2_HIGHMEM_4", "E2_HIGHMEM_8", "E2_HIGHMEM_16", "E2_HIGHCPU_2", "E2_HIGHCPU_4", "E2_HIGHCPU_8", "E2_HIGHCPU_16", "E2_HIGHCPU_32", "N2_STANDARD_2", "N2_STANDARD_4", "N2_STANDARD_8", "N2_STANDARD_16", "N2_STANDARD_32", "N2_STANDARD_48", "N2_STANDARD_64", "N2_STANDARD_80", "N2_STANDARD_96", "N2_STANDARD_128", "N2_HIGHMEM_2", "N2_HIGHMEM_4", "N2_HIGHMEM_8", "N2_HIGHMEM_16", "N2_HIGHMEM_32", "N2_HIGHMEM_48", "N2_HIGHMEM_64", "N2_HIGHMEM_80", "N2_HIGHMEM_96", "N2_HIGHMEM_128", "N2_HIGHCPU_2", "N2_HIGHCPU_4", "N2_HIGHCPU_8", "N2_HIGHCPU_16", "N2_HIGHCPU_32", "N2_HIGHCPU_48", "N2_HIGHCPU_64", "N2_HIGHCPU_80", "N2_HIGHCPU_96", "N2D_STANDARD_2", "N2D_STANDARD_4", "N2D_STANDARD_8", "N2D_STANDARD_16", "N2D_STANDARD_32", "N2D_STANDARD_48", "N2D_STANDARD_64", "N2D_STANDARD_80", "N2D_STANDARD_96", "N2D_STANDARD_128", "N2D_STANDARD_224", "N2D_HIGHMEM_2", "N2D_HIGHMEM_4", "N2D_HIGHMEM_8", "N2D_HIGHMEM_16", "N2D_HIGHMEM_32", "N2D_HIGHMEM_48", "N2D_HIGHMEM_64", "N2D_HIGHMEM_80", "N2D_HIGHMEM_96", "N2D_HIGHCPU_2", "N2D_HIGHCPU_4", "N2D_HIGHCPU_8", "N2D_HIGHCPU_16", "N2D_HIGHCPU_32", "N2D_HIGHCPU_48", "N2D_HIGHCPU_64", "N2D_HIGHCPU_80", "N2D_HIGHCPU_96", "N2D_HIGHCPU_128", "N2D_HIGHCPU_224", "C2_STANDARD_4", "C2_STANDARD_8", "C2_STANDARD_16", "C2_STANDARD_30", "C2_STANDARD_60", "C2D_STANDARD_2", "C2D_STANDARD_4", "C2D_STANDARD_8", "C2D_STANDARD_16", "C2D_STANDARD_32", "C2D_STANDARD_56", "C2D_STANDARD_112", "C2D_HIGHCPU_2", "C2D_HIGHCPU_4", "C2D_HIGHCPU_8", "C2D_HIGHCPU_16", "C2D_HIGHCPU_32", "C2D_HIGHCPU_56", "C2D_HIGHCPU_112", "C2D_HIGHMEM_2", "C2D_HIGHMEM_4", "C2D_HIGHMEM_8", "C2D_HIGHMEM_16", "C2D_HIGHMEM_32", "C2D_HIGHMEM_56", "C2D_HIGHMEM_112", "G2_STANDARD_4", "G2_STANDARD_8", "G2_STANDARD_12", "G2_STANDARD_16", "G2_STANDARD_24", "G2_STANDARD_32", "G2_STANDARD_48", "G2_STANDARD_96", "C3_STANDARD_4", "C3_STANDARD_8", "C3_STANDARD_22", "C3_STANDARD_44", "C3_STANDARD_88", "C3_STANDARD_176", "C3_HIGHCPU_4", "C3_HIGHCPU_8", "C3_HIGHCPU_22", "C3_HIGHCPU_44", "C3_HIGHCPU_88", "C3_HIGHCPU_176", "C3_HIGHMEM_4", "C3_HIGHMEM_8", "C3_HIGHMEM_22", "C3_HIGHMEM_44", "C3_HIGHMEM_88", "C3_HIGHMEM_176"], "enumDescriptions": ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], "type": "string"}, "trackingLabels": {"additionalProperties": {"type": "string"}, "description": "Billing tracking labels. They do not contain any user data but only the labels set by Vertex Core Infra itself. Tracking labels' keys are defined with special format: goog-[\\p{Ll}\\p{N}]+ E.g. \"key\": \"goog-k8s-cluster-name\",\"value\": \"us-east1-b4rk\"", "type": "object"}}, "type": "object"}, "InfraUsage": {"description": "LINT: LEGACY_NAMES Infra Usage of billing metrics.", "id": "InfraUsage", "properties": {"cpuMetrics": {"description": "Aggregated core metrics since requested start_time.", "items": {"$ref": "CpuMetric"}, "type": "array"}, "diskMetrics": {"description": "Aggregated persistent disk metrics since requested start_time.", "items": {"$ref": "DiskMetric"}, "type": "array"}, "gpuMetrics": {"description": "Aggregated gpu metrics since requested start_time.", "items": {"$ref": "GpuMetric"}, "type": "array"}, "ramMetrics": {"description": "Aggregated ram metrics since requested start_time.", "items": {"$ref": "RamMetric"}, "type": "array"}, "tpuMetrics": {"description": "Aggregated tpu metrics since requested start_time.", "items": {"$ref": "TpuMetric"}, "type": "array"}}, "type": "object"}, "ModerateTextRequest": {"description": "The document moderation request message.", "id": "ModerateTextRequest", "properties": {"document": {"$ref": "Document", "description": "Required. Input document."}}, "type": "object"}, "ModerateTextResponse": {"description": "The document moderation response message.", "id": "ModerateTextResponse", "properties": {"moderationCategories": {"description": "Harmful and sensitive categories representing the input document.", "items": {"$ref": "ClassificationCategory"}, "type": "array"}}, "type": "object"}, "PartOfSpeech": {"description": "Represents part of speech information for a token. Parts of speech are as defined in http://www.lrec-conf.org/proceedings/lrec2012/pdf/274_Paper.pdf", "id": "PartOfSpeech", "properties": {"aspect": {"description": "The grammatical aspect.", "enum": ["ASPECT_UNKNOWN", "PERFECTIVE", "IMPERFECTIVE", "PROGRESSIVE"], "enumDescriptions": ["Aspect is not applicable in the analyzed language or is not predicted.", "Perfective", "Imperfective", "Progressive"], "type": "string"}, "case": {"description": "The grammatical case.", "enum": ["CASE_UNKNOWN", "ACCUSATIVE", "ADVERBIAL", "COMPLEMENTIVE", "DATIVE", "GENITIVE", "INSTRUMENTAL", "LOCATIVE", "NOMINATIVE", "OBLIQUE", "PARTITIVE", "PREPOSITIONAL", "REFLEXIVE_CASE", "RELATIVE_CASE", "VOCATIVE"], "enumDescriptions": ["Case is not applicable in the analyzed language or is not predicted.", "Accusative", "Adverbial", "Complementive", "Dative", "Genitive", "Instrumental", "Locative", "Nominative", "Oblique", "Partitive", "Prepositional", "Reflexive", "Relative", "Vocative"], "type": "string"}, "form": {"description": "The grammatical form.", "enum": ["FORM_UNKNOWN", "ADNOMIAL", "AUXILIARY", "COMPLEMENTIZER", "FINAL_ENDING", "GERUND", "REALIS", "IRREALIS", "SHORT", "LONG", "ORDER", "SPECIFIC"], "enumDescriptions": ["Form is not applicable in the analyzed language or is not predicted.", "Adnomial", "Auxiliary", "Complementizer", "Final ending", "<PERSON><PERSON><PERSON>", "Realis", "<PERSON><PERSON><PERSON>", "Short form", "Long form", "Order form", "Specific form"], "type": "string"}, "gender": {"description": "The grammatical gender.", "enum": ["GENDER_UNKNOWN", "FEMININE", "MASCULINE", "NEUTER"], "enumDescriptions": ["Gender is not applicable in the analyzed language or is not predicted.", "Feminine", "Ma<PERSON><PERSON><PERSON>", "Neuter"], "type": "string"}, "mood": {"description": "The grammatical mood.", "enum": ["MOOD_UNKNOWN", "CONDITIONAL_MOOD", "IMPERATIVE", "INDICATIVE", "INTERROGATIVE", "JUSSIVE", "SUBJUNCTIVE"], "enumDescriptions": ["Mood is not applicable in the analyzed language or is not predicted.", "Conditional", "Imperative", "Indicative", "Interrogative", "<PERSON><PERSON><PERSON>", "Subjunctive"], "type": "string"}, "number": {"description": "The grammatical number.", "enum": ["NUMBER_UNKNOWN", "SINGULAR", "PLURAL", "DUAL"], "enumDescriptions": ["Number is not applicable in the analyzed language or is not predicted.", "Singular", "Plural", "Dual"], "type": "string"}, "person": {"description": "The grammatical person.", "enum": ["PERSON_UNKNOWN", "FIRST", "SECOND", "THIRD", "REFLEXIVE_PERSON"], "enumDescriptions": ["Person is not applicable in the analyzed language or is not predicted.", "First", "Second", "Third", "Reflexive"], "type": "string"}, "proper": {"description": "The grammatical properness.", "enum": ["PROPER_UNKNOWN", "PROPER", "NOT_PROPER"], "enumDescriptions": ["Proper is not applicable in the analyzed language or is not predicted.", "Proper", "Not proper"], "type": "string"}, "reciprocity": {"description": "The grammatical reciprocity.", "enum": ["RECIPROCITY_UNKNOWN", "RECIPROCAL", "NON_RECIPROCAL"], "enumDescriptions": ["Reciprocity is not applicable in the analyzed language or is not predicted.", "Reciprocal", "Non-reciprocal"], "type": "string"}, "tag": {"description": "The part of speech tag.", "enum": ["UNKNOWN", "ADJ", "ADP", "ADV", "CONJ", "DET", "NOUN", "NUM", "PRON", "PRT", "PUNCT", "VERB", "X", "AFFIX"], "enumDescriptions": ["Unknown", "Adjective", "Adposition (preposition and postposition)", "Adverb", "Conjunction", "Determiner", "Noun (common and proper)", "Cardinal number", "Pronoun", "Particle or other function word", "Punctuation", "Verb (all tenses and modes)", "Other: foreign words, typos, abbreviations", "Affix"], "type": "string"}, "tense": {"description": "The grammatical tense.", "enum": ["TENSE_UNKNOWN", "CONDITIONAL_TENSE", "FUTURE", "PAST", "PRESENT", "IMPERFECT", "PLUPERFECT"], "enumDescriptions": ["Tense is not applicable in the analyzed language or is not predicted.", "Conditional", "Future", "Past", "Present", "Imperfect", "Pluperfect"], "type": "string"}, "voice": {"description": "The grammatical voice.", "enum": ["VOICE_UNKNOWN", "ACTIVE", "CAUSATIVE", "PASSIVE"], "enumDescriptions": ["Voice is not applicable in the analyzed language or is not predicted.", "Active", "Causative", "Passive"], "type": "string"}}, "type": "object"}, "RamMetric": {"id": "RamMetric", "properties": {"gibSec": {"description": "Required. VM memory in Gigabyte second, e.g. 3600. Using int64 type to match billing metrics definition.", "format": "int64", "type": "string"}, "machineSpec": {"description": "Required. Machine spec, e.g. N1_STANDARD_4.", "enum": ["UNKNOWN_MACHINE_SPEC", "N1_STANDARD_2", "N1_STANDARD_4", "N1_STANDARD_8", "N1_STANDARD_16", "N1_STANDARD_32", "N1_STANDARD_64", "N1_STANDARD_96", "N1_HIGHMEM_2", "N1_HIGHMEM_4", "N1_HIGHMEM_8", "N1_HIGHMEM_16", "N1_HIGHMEM_32", "N1_HIGHMEM_64", "N1_HIGHMEM_96", "N1_HIGHCPU_2", "N1_HIGHCPU_4", "N1_HIGHCPU_8", "N1_HIGHCPU_16", "N1_HIGHCPU_32", "N1_HIGHCPU_64", "N1_HIGHCPU_96", "A2_HIGHGPU_1G", "A2_HIGHGPU_2G", "A2_HIGHGPU_4G", "A2_HIGHGPU_8G", "A2_MEGAGPU_16G", "A2_ULTRAGPU_1G", "A2_ULTRAGPU_2G", "A2_ULTRAGPU_4G", "A2_ULTRAGPU_8G", "A3_HIGHGPU_1G", "A3_HIGHGPU_2G", "A3_HIGHGPU_4G", "A3_HIGHGPU_8G", "A3_MEGAGPU_8G", "A3_ULTRAGPU_8G", "A3_EDGEGPU_8G", "A4_HIGHGPU_8G", "A4X_HIGHGPU_4G", "E2_STANDARD_2", "E2_STANDARD_4", "E2_STANDARD_8", "E2_STANDARD_16", "E2_STANDARD_32", "E2_HIGHMEM_2", "E2_HIGHMEM_4", "E2_HIGHMEM_8", "E2_HIGHMEM_16", "E2_HIGHCPU_2", "E2_HIGHCPU_4", "E2_HIGHCPU_8", "E2_HIGHCPU_16", "E2_HIGHCPU_32", "N2_STANDARD_2", "N2_STANDARD_4", "N2_STANDARD_8", "N2_STANDARD_16", "N2_STANDARD_32", "N2_STANDARD_48", "N2_STANDARD_64", "N2_STANDARD_80", "N2_STANDARD_96", "N2_STANDARD_128", "N2_HIGHMEM_2", "N2_HIGHMEM_4", "N2_HIGHMEM_8", "N2_HIGHMEM_16", "N2_HIGHMEM_32", "N2_HIGHMEM_48", "N2_HIGHMEM_64", "N2_HIGHMEM_80", "N2_HIGHMEM_96", "N2_HIGHMEM_128", "N2_HIGHCPU_2", "N2_HIGHCPU_4", "N2_HIGHCPU_8", "N2_HIGHCPU_16", "N2_HIGHCPU_32", "N2_HIGHCPU_48", "N2_HIGHCPU_64", "N2_HIGHCPU_80", "N2_HIGHCPU_96", "N2D_STANDARD_2", "N2D_STANDARD_4", "N2D_STANDARD_8", "N2D_STANDARD_16", "N2D_STANDARD_32", "N2D_STANDARD_48", "N2D_STANDARD_64", "N2D_STANDARD_80", "N2D_STANDARD_96", "N2D_STANDARD_128", "N2D_STANDARD_224", "N2D_HIGHMEM_2", "N2D_HIGHMEM_4", "N2D_HIGHMEM_8", "N2D_HIGHMEM_16", "N2D_HIGHMEM_32", "N2D_HIGHMEM_48", "N2D_HIGHMEM_64", "N2D_HIGHMEM_80", "N2D_HIGHMEM_96", "N2D_HIGHCPU_2", "N2D_HIGHCPU_4", "N2D_HIGHCPU_8", "N2D_HIGHCPU_16", "N2D_HIGHCPU_32", "N2D_HIGHCPU_48", "N2D_HIGHCPU_64", "N2D_HIGHCPU_80", "N2D_HIGHCPU_96", "N2D_HIGHCPU_128", "N2D_HIGHCPU_224", "C2_STANDARD_4", "C2_STANDARD_8", "C2_STANDARD_16", "C2_STANDARD_30", "C2_STANDARD_60", "C2D_STANDARD_2", "C2D_STANDARD_4", "C2D_STANDARD_8", "C2D_STANDARD_16", "C2D_STANDARD_32", "C2D_STANDARD_56", "C2D_STANDARD_112", "C2D_HIGHCPU_2", "C2D_HIGHCPU_4", "C2D_HIGHCPU_8", "C2D_HIGHCPU_16", "C2D_HIGHCPU_32", "C2D_HIGHCPU_56", "C2D_HIGHCPU_112", "C2D_HIGHMEM_2", "C2D_HIGHMEM_4", "C2D_HIGHMEM_8", "C2D_HIGHMEM_16", "C2D_HIGHMEM_32", "C2D_HIGHMEM_56", "C2D_HIGHMEM_112", "G2_STANDARD_4", "G2_STANDARD_8", "G2_STANDARD_12", "G2_STANDARD_16", "G2_STANDARD_24", "G2_STANDARD_32", "G2_STANDARD_48", "G2_STANDARD_96", "C3_STANDARD_4", "C3_STANDARD_8", "C3_STANDARD_22", "C3_STANDARD_44", "C3_STANDARD_88", "C3_STANDARD_176", "C3_HIGHCPU_4", "C3_HIGHCPU_8", "C3_HIGHCPU_22", "C3_HIGHCPU_44", "C3_HIGHCPU_88", "C3_HIGHCPU_176", "C3_HIGHMEM_4", "C3_HIGHMEM_8", "C3_HIGHMEM_22", "C3_HIGHMEM_44", "C3_HIGHMEM_88", "C3_HIGHMEM_176"], "enumDescriptions": ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], "type": "string"}, "memories": {"description": "Required. VM memory in gb.", "format": "double", "type": "number"}, "ramType": {"description": "Required. Type of ram.", "enum": ["UNKNOWN_RAM_TYPE", "A2", "A3", "A4", "A4X", "C2", "C2D", "CUSTOM", "E2", "G2", "C3", "M2", "M1", "N1", "N2_CUSTOM", "N2", "N2D"], "enumDescriptions": ["", "", "", "", "", "COMPUTE_OPTIMIZED", "", "", "", "", "", "MEMORY_OPTIMIZED_UPGRADE_PREMIUM", "MEMORY_OPTIMIZED", "", "", "", ""], "type": "string"}, "trackingLabels": {"additionalProperties": {"type": "string"}, "description": "Billing tracking labels. They do not contain any user data but only the labels set by Vertex Core Infra itself. Tracking labels' keys are defined with special format: goog-[\\p{Ll}\\p{N}]+ E.g. \"key\": \"goog-k8s-cluster-name\",\"value\": \"us-east1-b4rk\"", "type": "object"}}, "type": "object"}, "Sentence": {"description": "Represents a sentence in the input document.", "id": "Sentence", "properties": {"sentiment": {"$ref": "Sentiment", "description": "For calls to AnalyzeSentiment or if AnnotateTextRequest.Features.extract_document_sentiment is set to true, this field will contain the sentiment for the sentence."}, "text": {"$ref": "TextSpan", "description": "The sentence text."}}, "type": "object"}, "Sentiment": {"description": "Represents the feeling associated with the entire text or entities in the text.", "id": "Sentiment", "properties": {"magnitude": {"description": "A non-negative number in the [0, +inf) range, which represents the absolute magnitude of sentiment regardless of score (positive or negative).", "format": "float", "type": "number"}, "score": {"description": "Sentiment score between -1.0 (negative sentiment) and 1.0 (positive sentiment).", "format": "float", "type": "number"}}, "type": "object"}, "Status": {"description": "The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each `Status` message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors).", "id": "Status", "properties": {"code": {"description": "The status code, which should be an enum value of google.rpc.Code.", "format": "int32", "type": "integer"}, "details": {"description": "A list of messages that carry the error details. There is a common set of message types for APIs to use.", "items": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "type": "object"}, "type": "array"}, "message": {"description": "A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the google.rpc.Status.details field, or localized by the client.", "type": "string"}}, "type": "object"}, "TextSpan": {"description": "Represents a text span in the input document.", "id": "TextSpan", "properties": {"beginOffset": {"description": "The API calculates the beginning offset of the content in the original document according to the EncodingType specified in the API request.", "format": "int32", "type": "integer"}, "content": {"description": "The content of the text span, which is a substring of the document.", "type": "string"}}, "type": "object"}, "Token": {"description": "Represents the smallest syntactic building block of the text.", "id": "Token", "properties": {"dependencyEdge": {"$ref": "DependencyEdge", "description": "Dependency tree parse for this token."}, "lemma": {"description": "[Lemma](https://en.wikipedia.org/wiki/Lemma_%28morphology%29) of the token.", "type": "string"}, "partOfSpeech": {"$ref": "PartOfSpeech", "description": "Parts of speech tag for this token."}, "text": {"$ref": "TextSpan", "description": "The token text."}}, "type": "object"}, "TpuMetric": {"id": "TpuMetric", "properties": {"tpuSec": {"description": "Required. Seconds of TPU usage, e.g. 3600.", "format": "int64", "type": "string"}, "tpuType": {"description": "Required. Type of TPU, e.g. TPU_V2, TPU_V3_POD.", "enum": ["UNKNOWN_TPU_TYPE", "TPU_V2_POD", "TPU_V2", "TPU_V3_POD", "TPU_V3", "TPU_V5_LITEPOD"], "enumDescriptions": ["", "", "", "", "", ""], "type": "string"}}, "type": "object"}, "XPSArrayStats": {"description": "The data statistics of a series of ARRAY values.", "id": "XPSArrayStats", "properties": {"commonStats": {"$ref": "XPSCommonStats"}, "memberStats": {"$ref": "XPSDataStats", "description": "Stats of all the values of all arrays, as if they were a single long series of data. The type depends on the element type of the array."}}, "type": "object"}, "XPSBatchPredictResponse": {"id": "XPSBatchPredictResponse", "properties": {"exampleSet": {"$ref": "XPSExampleSet", "description": "Examples for batch prediction result. Under full API implementation, results are stored in shared RecordIO of AnnotatedExample protobufs, the annotations field of which is populated by XPS backend."}}, "type": "object"}, "XPSBoundingBoxMetricsEntry": {"description": "Bounding box matching model metrics for a single intersection-over-union threshold and multiple label match confidence thresholds.", "id": "XPSBoundingBoxMetricsEntry", "properties": {"confidenceMetricsEntries": {"description": "Metrics for each label-match confidence_threshold from 0.05,0.10,...,0.95,0.96,0.97,0.98,0.99.", "items": {"$ref": "XPSBoundingBoxMetricsEntryConfidenceMetricsEntry"}, "type": "array"}, "iouThreshold": {"description": "The intersection-over-union threshold value used to compute this metrics entry.", "format": "float", "type": "number"}, "meanAveragePrecision": {"description": "The mean average precision.", "format": "float", "type": "number"}}, "type": "object"}, "XPSBoundingBoxMetricsEntryConfidenceMetricsEntry": {"description": "Metrics for a single confidence threshold.", "id": "XPSBoundingBoxMetricsEntryConfidenceMetricsEntry", "properties": {"confidenceThreshold": {"description": "The confidence threshold value used to compute the metrics.", "format": "float", "type": "number"}, "f1Score": {"description": "The harmonic mean of recall and precision.", "format": "float", "type": "number"}, "precision": {"description": "Precision for the given confidence threshold.", "format": "float", "type": "number"}, "recall": {"description": "Recall for the given confidence threshold.", "format": "float", "type": "number"}}, "type": "object"}, "XPSCategoryStats": {"description": "The data statistics of a series of CATEGORY values.", "id": "XPSCategoryStats", "properties": {"commonStats": {"$ref": "XPSCommonStats"}, "topCategoryStats": {"description": "The statistics of the top 20 CATEGORY values, ordered by CategoryStats.SingleCategoryStats.count.", "items": {"$ref": "XPSCategoryStatsSingleCategoryStats"}, "type": "array"}}, "type": "object"}, "XPSCategoryStatsSingleCategoryStats": {"description": "The statistics of a single CATEGORY value.", "id": "XPSCategoryStatsSingleCategoryStats", "properties": {"count": {"description": "The number of occurrences of this value in the series.", "format": "int64", "type": "string"}, "value": {"description": "The CATEGORY value.", "type": "string"}}, "type": "object"}, "XPSClassificationEvaluationMetrics": {"description": "Model evaluation metrics for classification problems. It can be used for image and video classification. Next tag: 9.", "id": "XPSClassificationEvaluationMetrics", "properties": {"auPrc": {"description": "The Area under precision recall curve metric.", "format": "float", "type": "number"}, "auRoc": {"description": "The Area Under Receiver Operating Characteristic curve metric. Micro-averaged for the overall evaluation.", "format": "float", "type": "number"}, "baseAuPrc": {"description": "The Area under precision recall curve metric based on priors.", "format": "float", "type": "number"}, "confidenceMetricsEntries": {"description": "Metrics that have confidence thresholds. Precision-recall curve can be derived from it.", "items": {"$ref": "XPSConfidenceMetricsEntry"}, "type": "array"}, "confusionMatrix": {"$ref": "XPSConfusionMatrix", "description": "Confusion matrix of the evaluation. Only set for MULTICLASS classification problems where number of annotation specs is no more than 10. Only set for model level evaluation, not for evaluation per label."}, "evaluatedExamplesCount": {"description": "The number of examples used for model evaluation.", "format": "int32", "type": "integer"}, "logLoss": {"description": "The Log Loss metric.", "format": "float", "type": "number"}}, "type": "object"}, "XPSColorMap": {"description": "Map from color to display name. Will only be used by Image Segmentation for uCAIP.", "id": "XPSColorMap", "properties": {"annotationSpecIdToken": {"description": "Should be used during training.", "type": "string"}, "color": {"$ref": "Color", "deprecated": true, "description": "This type is deprecated in favor of the IntColor below. This is because google.type.Color represent color has a float which semantically does not reflect discrete classes/categories concept. Moreover, to handle it well we need to have some tolerance when converting to a discretized color. As such, the recommendation is to have API surface still use google.type.Color while internally IntColor is used."}, "displayName": {"description": "Should be used during preprocessing.", "type": "string"}, "intColor": {"$ref": "XPSColorMapIntColor"}}, "type": "object"}, "XPSColorMapIntColor": {"description": "RGB color and each channel is represented by an integer.", "id": "XPSColorMapIntColor", "properties": {"blue": {"description": "The value should be in range of [0, 255].", "format": "int32", "type": "integer"}, "green": {"description": "The value should be in range of [0, 255].", "format": "int32", "type": "integer"}, "red": {"description": "The value should be in range of [0, 255].", "format": "int32", "type": "integer"}}, "type": "object"}, "XPSColumnSpec": {"id": "XPSColumnSpec", "properties": {"columnId": {"description": "The unique id of the column. When Preprocess, the Tables BE will popuate the order id of the column, which reflects the order of the column inside the table, i.e. 0 means the first column in the table, N-1 means the last column. AutoML BE will persist this order id in Spanner and set the order id here when calling RefreshTablesStats and Train. Note: it's different than the column_spec_id that is generated in AutoML BE.", "format": "int32", "type": "integer"}, "dataStats": {"$ref": "XPSDataStats", "description": "The data stats of the column. It's outputed in RefreshTablesStats and a required input for Train."}, "dataType": {"$ref": "XPSDataType", "description": "The data type of the column. It's outputed in Preprocess rpc and a required input for RefreshTablesStats and Train."}, "displayName": {"description": "The display name of the column. It's outputed in Preprocess and a required input for RefreshTablesStats and Train.", "type": "string"}, "forecastingMetadata": {"$ref": "XPSColumnSpecForecastingMetadata"}, "topCorrelatedColumns": {"description": "It's outputed in RefreshTablesStats, and a required input in Train.", "items": {"$ref": "XPSColumnSpecCorrelatedColumn"}, "type": "array"}}, "type": "object"}, "XPSColumnSpecCorrelatedColumn": {"description": "Identifies a table's column, and its correlation with the column this ColumnSpec describes.", "id": "XPSColumnSpecCorrelatedColumn", "properties": {"columnId": {"format": "int32", "type": "integer"}, "correlationStats": {"$ref": "XPSCorrelationStats"}}, "type": "object"}, "XPSColumnSpecForecastingMetadata": {"id": "XPSColumnSpecForecastingMetadata", "properties": {"columnType": {"description": "The type of the column for FORECASTING model training purposes.", "enum": ["COLUMN_TYPE_UNSPECIFIED", "KEY", "KEY_METADATA", "TIME_SERIES_AVAILABLE_PAST_ONLY", "TIME_SERIES_AVAILABLE_PAST_AND_FUTURE"], "enumDescriptions": ["An un-set value of this enum.", "Key columns are used to identify timeseries.", "This column contains information describing static properties of the entities identified by the key column(s) (e.g. city's ZIP code).", "This column contains information for the given entity, at any time poinrt, they are only available in the time series before.", "This column contains information for the given entity is known both for the past and the sufficiently far future."], "type": "string"}}, "type": "object"}, "XPSCommonStats": {"description": "Common statistics for a column with a specified data type.", "id": "XPSCommonStats", "properties": {"distinctValueCount": {"format": "int64", "type": "string"}, "nullValueCount": {"format": "int64", "type": "string"}, "validValueCount": {"format": "int64", "type": "string"}}, "type": "object"}, "XPSConfidenceMetricsEntry": {"description": "ConfidenceMetricsEntry includes generic precision, recall, f1 score etc. Next tag: 16.", "id": "XPSConfidenceMetricsEntry", "properties": {"confidenceThreshold": {"description": "Metrics are computed with an assumption that the model never return predictions with score lower than this value.", "format": "float", "type": "number"}, "f1Score": {"description": "The harmonic mean of recall and precision.", "format": "float", "type": "number"}, "f1ScoreAt1": {"description": "The harmonic mean of recall_at1 and precision_at1.", "format": "float", "type": "number"}, "falseNegativeCount": {"description": "The number of ground truth labels that are not matched by a model created label.", "format": "int64", "type": "string"}, "falsePositiveCount": {"description": "The number of model created labels that do not match a ground truth label.", "format": "int64", "type": "string"}, "falsePositiveRate": {"description": "False Positive Rate for the given confidence threshold.", "format": "float", "type": "number"}, "falsePositiveRateAt1": {"description": "The False Positive Rate when only considering the label that has the highest prediction score and not below the confidence threshold for each example.", "format": "float", "type": "number"}, "positionThreshold": {"description": "Metrics are computed with an assumption that the model always returns at most this many predictions (ordered by their score, descendingly), but they all still need to meet the confidence_threshold.", "format": "int32", "type": "integer"}, "precision": {"description": "Precision for the given confidence threshold.", "format": "float", "type": "number"}, "precisionAt1": {"description": "The precision when only considering the label that has the highest prediction score and not below the confidence threshold for each example.", "format": "float", "type": "number"}, "recall": {"description": "Recall (true positive rate) for the given confidence threshold.", "format": "float", "type": "number"}, "recallAt1": {"description": "The recall (true positive rate) when only considering the label that has the highest prediction score and not below the confidence threshold for each example.", "format": "float", "type": "number"}, "trueNegativeCount": {"description": "The number of labels that were not created by the model, but if they would, they would not match a ground truth label.", "format": "int64", "type": "string"}, "truePositiveCount": {"description": "The number of model created labels that match a ground truth label.", "format": "int64", "type": "string"}}, "type": "object"}, "XPSConfusionMatrix": {"description": "Confusion matrix of the model running the classification.", "id": "XPSConfusionMatrix", "properties": {"annotationSpecIdToken": {"description": "For the following three repeated fields, only one is intended to be set. annotation_spec_id_token is preferable to be set. ID tokens of the annotation specs used in the confusion matrix.", "items": {"type": "string"}, "type": "array"}, "category": {"description": "Category (mainly for segmentation). Set only for image segmentation models. Note: uCAIP Image Segmentation should use annotation_spec_id_token.", "items": {"format": "int32", "type": "integer"}, "type": "array"}, "row": {"description": "Rows in the confusion matrix. The number of rows is equal to the size of `annotation_spec_id_token`. `row[i].value[j]` is the number of examples that have ground truth of the `annotation_spec_id_token[i]` and are predicted as `annotation_spec_id_token[j]` by the model being evaluated.", "items": {"$ref": "XPSConfusionMatrixRow"}, "type": "array"}, "sentimentLabel": {"description": "Sentiment labels used in the confusion matrix. Set only for text sentiment models. For AutoML Text Revamp, use `annotation_spec_id_token` instead and leave this field empty.", "items": {"format": "int32", "type": "integer"}, "type": "array"}}, "type": "object"}, "XPSConfusionMatrixRow": {"description": "A row in the confusion matrix.", "id": "XPSConfusionMatrixRow", "properties": {"count": {"description": "Same as above except intended to represent other counts (for e.g. for segmentation this is pixel count). NOTE(params): Only example_count or count is set (oneoff does not support repeated fields unless they are embedded inside another message).", "items": {"format": "int64", "type": "string"}, "type": "array"}, "exampleCount": {"description": "Value of the specific cell in the confusion matrix. The number of values each row has (i.e. the length of the row) is equal to the length of the annotation_spec_id_token field.", "items": {"format": "int32", "type": "integer"}, "type": "array"}}, "type": "object"}, "XPSCoreMlFormat": {"description": "A model format used for iOS mobile devices.", "id": "XPSCoreMlFormat", "properties": {}, "type": "object"}, "XPSCorrelationStats": {"description": "A correlation statistics between two series of DataType values. The series may have differing DataType-s, but within a single series the DataType must be the same.", "id": "XPSCorrelationStats", "properties": {"cramersV": {"description": "The correlation value using the <PERSON><PERSON><PERSON>'s V measure.", "format": "double", "type": "number"}}, "type": "object"}, "XPSDataErrors": {"description": "Different types of errors and the stats associatesd with each error.", "id": "XPSDataErrors", "properties": {"count": {"description": "Number of records having errors associated with the enum.", "format": "int32", "type": "integer"}, "errorType": {"description": "Type of the error.", "enum": ["ERROR_TYPE_UNSPECIFIED", "UNSUPPORTED_AUDIO_FORMAT", "FILE_EXTENSION_MISMATCH_WITH_AUDIO_FORMAT", "FILE_TOO_LARGE", "MISSING_TRANSCRIPTION"], "enumDescriptions": ["Not specified.", "Audio format not in the formats by cloud-speech AutoML. Currently only wav and flac file formats are supported.", "File format differnt from what is specified in the file name extension.", "File too large. Maximum allowed size is 50 MB.", "Transcript is missing."], "type": "string"}}, "type": "object"}, "XPSDataStats": {"description": "The data statistics of a series of values that share the same DataType.", "id": "XPSDataStats", "properties": {"arrayStats": {"$ref": "XPSArrayStats", "description": "The statistics for ARRAY DataType."}, "categoryStats": {"$ref": "XPSCategoryStats", "description": "The statistics for CATEGORY DataType."}, "distinctValueCount": {"description": "The number of distinct values.", "format": "int64", "type": "string"}, "float64Stats": {"$ref": "XPSFloat64Stats", "description": "The statistics for FLOAT64 DataType."}, "nullValueCount": {"description": "The number of values that are null.", "format": "int64", "type": "string"}, "stringStats": {"$ref": "XPSStringStats", "description": "The statistics for STRING DataType."}, "structStats": {"$ref": "XPSStructStats", "description": "The statistics for STRUCT DataType."}, "timestampStats": {"$ref": "XPSTimestampStats", "description": "The statistics for TIMESTAMP DataType."}, "validValueCount": {"description": "The number of values that are valid.", "format": "int64", "type": "string"}}, "type": "object"}, "XPSDataType": {"description": "Indicated the type of data that can be stored in a structured data entity (e.g. a table).", "id": "XPSDataType", "properties": {"compatibleDataTypes": {"description": "The highly compatible data types to this data type.", "items": {"$ref": "XPSDataType"}, "type": "array"}, "listElementType": {"$ref": "XPSDataType", "description": "If type_code == ARRAY, then `list_element_type` is the type of the elements."}, "nullable": {"description": "If true, this DataType can also be `null`.", "type": "boolean"}, "structType": {"$ref": "XPSStructType", "description": "If type_code == STRUCT, then `struct_type` provides type information for the struct's fields."}, "timeFormat": {"description": "If type_code == TIMESTAMP then `time_format` provides the format in which that time field is expressed. The time_format must be written in `strftime` syntax. If time_format is not set, then the default format as described on the field is used.", "type": "string"}, "typeCode": {"description": "Required. The TypeCode for this type.", "enum": ["TYPE_CODE_UNSPECIFIED", "FLOAT64", "TIMESTAMP", "STRING", "ARRAY", "STRUCT", "CATEGORY"], "enumDescriptions": ["Not specified. Should not be used.", "Encoded as `number`, or the strings `\"NaN\"`, `\"Infinity\"`, or `\"-Infinity\"`.", "Must be between 0AD and 9999AD. Encoded as `string` according to time_format, or, if that format is not set, then in RFC 3339 `date-time` format, where `time-offset` = `\"Z\"` (e.g. 1985-04-12T23:20:50.52Z).", "Encoded as `string`.", "Encoded as `list`, where the list elements are represented according to list_element_type.", "Encoded as `struct`, where field values are represented according to struct_type.", "Values of this type are not further understood by AutoML, e.g. AutoML is unable to tell the order of values (as it could with FLOAT64), or is unable to say if one value contains another (as it could with STRING). Encoded as `string` (bytes should be base64-encoded, as described in RFC 4648, section 4)."], "type": "string"}}, "type": "object"}, "XPSDockerFormat": {"description": "A model format used for Docker containers. Use the params field to customize the container. The container is verified to work correctly on ubuntu 16.04 operating system.", "id": "XPSDockerFormat", "properties": {"cpuArchitecture": {"description": "Optional. Additional cpu information describing the requirements for the to be exported model files.", "enum": ["CPU_ARCHITECTURE_UNSPECIFIED", "CPU_ARCHITECTURE_X86_64"], "enumDescriptions": ["", ""], "type": "string"}, "gpuArchitecture": {"description": "Optional. Additional gpu information describing the requirements for the to be exported model files.", "enum": ["GPU_ARCHITECTURE_UNSPECIFIED", "GPU_ARCHITECTURE_NVIDIA"], "enumDescriptions": ["", ""], "type": "string"}}, "type": "object"}, "XPSEdgeTpuTfLiteFormat": {"description": "A model format used for [Edge TPU](https://cloud.google.com/edge-tpu/) devices.", "id": "XPSEdgeTpuTfLiteFormat", "properties": {}, "type": "object"}, "XPSEvaluationMetrics": {"description": "Contains xPS-specific model evaluation metrics either for a single annotation spec (label), or for the model overall. Next tag: 18.", "id": "XPSEvaluationMetrics", "properties": {"annotationSpecIdToken": {"description": "The annotation_spec for which this evaluation metrics instance had been created. Empty iff this is an overall model evaluation (like Tables evaluation metrics), i.e. aggregated across all labels. The value comes from the input annotations in AnnotatedExample. For MVP product or for text sentiment models where annotation_spec_id_token is not available, set label instead.", "type": "string"}, "category": {"description": "The integer category label for which this evaluation metric instance had been created. Valid categories are 0 or higher. Overall model evaluation should set this to negative values (rather than implicit zero). Only used for Image Segmentation (prefer to set annotation_spec_id_token instead). Note: uCAIP Image Segmentation should use annotation_spec_id_token.", "format": "int32", "type": "integer"}, "evaluatedExampleCount": {"description": "The number of examples used to create this evaluation metrics instance.", "format": "int32", "type": "integer"}, "imageClassificationEvalMetrics": {"$ref": "XPSClassificationEvaluationMetrics"}, "imageObjectDetectionEvalMetrics": {"$ref": "XPSImageObjectDetectionEvaluationMetrics"}, "imageSegmentationEvalMetrics": {"$ref": "XPSImageSegmentationEvaluationMetrics"}, "label": {"description": "The label for which this evaluation metrics instance had been created. Empty iff this is an overall model evaluation (like Tables evaluation metrics), i.e. aggregated across all labels. The label maps to AnnotationSpec.display_name in Public API protos. Only used by MVP implementation and text sentiment FULL implementation.", "type": "string"}, "regressionEvalMetrics": {"$ref": "XPSRegressionEvaluationMetrics"}, "tablesClassificationEvalMetrics": {"$ref": "XPSClassificationEvaluationMetrics"}, "tablesEvalMetrics": {"$ref": "XPSTablesEvaluationMetrics"}, "textClassificationEvalMetrics": {"$ref": "XPSClassificationEvaluationMetrics"}, "textExtractionEvalMetrics": {"$ref": "XPSTextExtractionEvaluationMetrics"}, "textSentimentEvalMetrics": {"$ref": "XPSTextSentimentEvaluationMetrics"}, "translationEvalMetrics": {"$ref": "XPSTranslationEvaluationMetrics"}, "videoActionRecognitionEvalMetrics": {"$ref": "XPSVideoActionRecognitionEvaluationMetrics"}, "videoClassificationEvalMetrics": {"$ref": "XPSClassificationEvaluationMetrics"}, "videoObjectTrackingEvalMetrics": {"$ref": "XPSVideoObjectTrackingEvaluationMetrics"}}, "type": "object"}, "XPSEvaluationMetricsSet": {"description": "Specifies location of model evaluation metrics.", "id": "XPSEvaluationMetricsSet", "properties": {"evaluationMetrics": {"description": "Inline EvaluationMetrics - should be relatively small. For passing large quantities of exhaustive metrics, use file_spec.", "items": {"$ref": "XPSEvaluationMetrics"}, "type": "array"}, "fileSpec": {"$ref": "XPSFileSpec", "description": "File spec containing evaluation metrics of a model, must point to RecordIO file(s) of intelligence.cloud.automl.xps.EvaluationMetrics messages."}, "numEvaluationMetrics": {"description": "Number of the evaluation metrics (usually one per label plus overall).", "format": "int64", "type": "string"}}, "type": "object"}, "XPSExampleSet": {"description": "Set of examples or input sources.", "id": "XPSExampleSet", "properties": {"fileSpec": {"$ref": "XPSFileSpec", "description": "File spec of the examples or input sources."}, "fingerprint": {"description": "Fingerprint of the example set.", "format": "int64", "type": "string"}, "numExamples": {"description": "Number of examples.", "format": "int64", "type": "string"}, "numInputSources": {"description": "Number of input sources.", "format": "int64", "type": "string"}}, "type": "object"}, "XPSExportModelOutputConfig": {"id": "XPSExportModelOutputConfig", "properties": {"coreMlFormat": {"$ref": "XPSCoreMlFormat"}, "dockerFormat": {"$ref": "XPSDockerFormat"}, "edgeTpuTfLiteFormat": {"$ref": "XPSEdgeTpuTfLiteFormat"}, "exportFirebaseAuxiliaryInfo": {"description": "For any model and format: If true, will additionally export FirebaseExportedModelInfo in a firebase.txt file.", "type": "boolean"}, "outputGcrUri": {"description": "The Google Contained Registry path the exported files to be pushed to. This location is set if the exported format is DOCKDER.", "type": "string"}, "outputGcsUri": {"description": "The Google Cloud Storage directory where XPS will output the exported models and related files. Format: gs://bucket/directory", "type": "string"}, "tfJsFormat": {"$ref": "XPSTfJsFormat"}, "tfLiteFormat": {"$ref": "XPSTfLiteFormat"}, "tfSavedModelFormat": {"$ref": "XPSTfSavedModelFormat"}}, "type": "object"}, "XPSFileSpec": {"description": "Spec of input and output files, on external file systems (for example, Colossus Namespace System or Google Cloud Storage).", "id": "XPSFileSpec", "properties": {"directoryPath": {"deprecated": true, "description": "Deprecated. Use file_spec.", "type": "string"}, "fileFormat": {"enum": ["FILE_FORMAT_UNKNOWN", "FILE_FORMAT_SSTABLE", "FILE_FORMAT_TRANSLATION_RKV", "FILE_FORMAT_RECORDIO", "FILE_FORMAT_RAW_CSV", "FILE_FORMAT_RAW_CAPACITOR"], "enumDeprecated": [false, true, false, false, false, false], "enumDescriptions": ["", "", "Internal format for parallel text data used by Google Translate.", "", "Only the lexicographically first file described by the file_spec contains the header line.", ""], "type": "string"}, "fileSpec": {"description": "Single file path, or file pattern of format \"/path/to/file@shard_count\". E.g. /cns/cell-d/somewhere/file@2 is expanded to two files: /cns/cell-d/somewhere/file-00000-of-00002 and /cns/cell-d/somewhere/file-00001-of-00002.", "type": "string"}, "singleFilePath": {"deprecated": true, "description": "Deprecated. Use file_spec.", "type": "string"}}, "type": "object"}, "XPSFloat64Stats": {"description": "The data statistics of a series of FLOAT64 values.", "id": "XPSFloat64Stats", "properties": {"commonStats": {"$ref": "XPSCommonStats"}, "histogramBuckets": {"description": "Histogram buckets of the data series. Sorted by the min value of the bucket, ascendingly, and the number of the buckets is dynamically generated. The buckets are non-overlapping and completely cover whole FLOAT64 range with min of first bucket being `\"-Infinity\"`, and max of the last one being `\"Infinity\"`.", "items": {"$ref": "XPSFloat64StatsHistogramBucket"}, "type": "array"}, "mean": {"description": "The mean of the series.", "format": "double", "type": "number"}, "quantiles": {"description": "Ordered from 0 to k k-quantile values of the data series of n values. The value at index i is, approximately, the i*n/k-th smallest value in the series; for i = 0 and i = k these are, respectively, the min and max values.", "items": {"format": "double", "type": "number"}, "type": "array"}, "standardDeviation": {"description": "The standard deviation of the series.", "format": "double", "type": "number"}}, "type": "object"}, "XPSFloat64StatsHistogramBucket": {"description": "A bucket of a histogram.", "id": "XPSFloat64StatsHistogramBucket", "properties": {"count": {"description": "The number of data values that are in the bucket, i.e. are between min and max values.", "format": "int64", "type": "string"}, "max": {"description": "The maximum value of the bucket, exclusive unless max = `\"Infinity\"`, in which case it's inclusive.", "format": "double", "type": "number"}, "min": {"description": "The minimum value of the bucket, inclusive.", "format": "double", "type": "number"}}, "type": "object"}, "XPSImageClassificationTrainResponse": {"id": "XPSImageClassificationTrainResponse", "properties": {"classCount": {"description": "Total number of classes.", "format": "int64", "type": "string"}, "exportModelSpec": {"$ref": "XPSImageExportModelSpec", "description": "Information of downloadable models that are pre-generated as part of training flow and will be persisted in AutoMl backend. Populated for AutoMl requests."}, "modelArtifactSpec": {"$ref": "XPSImageModelArtifactSpec", "description": "## The fields below are only populated under uCAIP request scope."}, "modelServingSpec": {"$ref": "XPSImageModelServingSpec"}, "stopReason": {"description": "Stop reason for training job, e.g. 'TRAIN_BUDGET_REACHED', 'MODEL_CONVERGED', 'MODEL_EARLY_STOPPED'.", "enum": ["TRAIN_STOP_REASON_UNSPECIFIED", "TRAIN_STOP_REASON_BUDGET_REACHED", "TRAIN_STOP_REASON_MODEL_CONVERGED", "TRAIN_STOP_REASON_MODEL_EARLY_STOPPED"], "enumDescriptions": ["", "", "Model fully converged, can not be resumbed training.", "Model early converged, can be further trained till full convergency."], "type": "string"}, "trainCostInNodeTime": {"description": "The actual cost to create this model. - For edge type model, the cost is expressed in node hour. - For cloud type model,the cost is expressed in compute hour. - Populated for models created before GA. To be deprecated after GA.", "format": "google-duration", "type": "string"}, "trainCostNodeSeconds": {"description": "The actual training cost, expressed in node seconds. Populated for models trained in node time.", "format": "int64", "type": "string"}}, "type": "object"}, "XPSImageExportModelSpec": {"description": "Information of downloadable models that are pre-generated as part of training flow and will be persisted in AutoMl backend. Upon receiving ExportModel request from user, AutoMl backend can serve the pre-generated models to user if exists (by copying the files from internal path to user provided location), otherwise, AutoMl backend will call xPS ExportModel API to generate the model on the fly with the requesting format.", "id": "XPSImageExportModelSpec", "properties": {"exportModelOutputConfig": {"description": "Contains the model format and internal location of the model files to be exported/downloaded. Use the Google Cloud Storage bucket name which is provided via TrainRequest.gcs_bucket_name to store the model files.", "items": {"$ref": "XPSExportModelOutputConfig"}, "type": "array"}}, "type": "object"}, "XPSImageModelArtifactSpec": {"description": "Stores the locations and related metadata of the model artifacts. Populated for uCAIP requests only.", "id": "XPSImageModelArtifactSpec", "properties": {"checkpointArtifact": {"$ref": "XPSModelArtifactItem", "description": "The Tensorflow checkpoint files. e.g. Used for resumable training."}, "exportArtifact": {"description": "The model binary files in different formats for model export.", "items": {"$ref": "XPSModelArtifactItem"}, "type": "array"}, "labelGcsUri": {"description": "Google Cloud Storage URI of decoded labels file for model export 'dict.txt'.", "type": "string"}, "servingArtifact": {"$ref": "XPSModelArtifactItem", "description": "The default model binary file used for serving (e.g. online predict, batch predict) via public Cloud AI Platform API."}, "tfJsBinaryGcsPrefix": {"description": "Google Cloud Storage URI prefix of Tensorflow JavaScript binary files 'groupX-shardXofX.bin'. Deprecated.", "type": "string"}, "tfLiteMetadataGcsUri": {"description": "Google Cloud Storage URI of Tensorflow Lite metadata 'tflite_metadata.json'.", "type": "string"}}, "type": "object"}, "XPSImageModelServingSpec": {"description": "Serving specification for image models.", "id": "XPSImageModelServingSpec", "properties": {"modelThroughputEstimation": {"description": "Populate under uCAIP request scope.", "items": {"$ref": "XPSImageModelServingSpecModelThroughputEstimation"}, "type": "array"}, "nodeQps": {"description": "An estimated value of how much traffic a node can serve. Populated for AutoMl request only.", "format": "double", "type": "number"}, "tfRuntimeVersion": {"description": "## The fields below are only populated under uCAIP request scope. https://cloud.google.com/ml-engine/docs/runtime-version-list", "type": "string"}}, "type": "object"}, "XPSImageModelServingSpecModelThroughputEstimation": {"id": "XPSImageModelServingSpecModelThroughputEstimation", "properties": {"computeEngineAcceleratorType": {"enum": ["UNSPECIFIED", "NVIDIA_TESLA_K80", "NVIDIA_TESLA_P100", "NVIDIA_TESLA_V100", "NVIDIA_TESLA_P4", "NVIDIA_TESLA_T4", "NVIDIA_TESLA_A100", "NVIDIA_A100_80GB", "NVIDIA_L4", "NVIDIA_H100_80GB", "NVIDIA_H100_MEGA_80GB", "NVIDIA_H200_141GB", "NVIDIA_B200", "NVIDIA_GB200", "TPU_V2", "TPU_V3", "TPU_V4_POD", "TPU_V5_LITEPOD"], "enumDescriptions": ["", "Nvidia Tesla K80 GPU.", "Nvidia Tesla P100 GPU.", "Nvidia Tesla V100 GPU.", "Nvidia Tesla P4 GPU.", "Nvidia Tesla T4 GPU.", "Nvidia Tesla A100 GPU.", "Nvidia A100 80GB GPU.", "Nvidia L4 GPU.", "Nvidia H100 80Gb GPU.", "Nvidia H100 80Gb GPU.", "Nvidia H200 141Gb GPU.", "Nvidia B200 GPU.", "Nvidia GB200 GPU.", "TPU v2 (JellyFish).", "TPU v3 (DragonFish).", "TPU_v4 (PufferFish).", "TPU v5 Lite Pods."], "type": "string"}, "latencyInMilliseconds": {"description": "Estimated latency.", "format": "double", "type": "number"}, "nodeQps": {"description": "The approximate qps a deployed node can serve.", "format": "double", "type": "number"}, "servomaticPartitionType": {"enum": ["PARTITION_TYPE_UNSPECIFIED", "PARTITION_ZERO", "PARTITION_REDUCED_HOMING", "PARTITION_JELLYFISH", "PARTITION_CPU", "PARTITION_CUSTOM_STORAGE_CPU"], "enumDescriptions": ["", "The default partition.", "It has significantly lower replication than partition-0 and is located in the US only. It also has a larger model size limit and higher default RAM quota than partition-0. Customers with batch traffic, US-based traffic, or very large models should use this partition. Capacity in this partition is significantly cheaper than partition-0.", "To be used by customers with Jellyfish-accelerated ops.", "The partition used by regionalized servomatic cloud regions.", "The partition used for loading models from custom storage."], "type": "string"}}, "type": "object"}, "XPSImageObjectDetectionEvaluationMetrics": {"description": "Model evaluation metrics for image object detection problems. Evaluates prediction quality of labeled bounding boxes.", "id": "XPSImageObjectDetectionEvaluationMetrics", "properties": {"boundingBoxMeanAveragePrecision": {"description": "The single metric for bounding boxes evaluation: the mean_average_precision averaged over all bounding_box_metrics_entries.", "format": "float", "type": "number"}, "boundingBoxMetricsEntries": {"description": "The bounding boxes match metrics for each Intersection-over-union threshold 0.05,0.10,...,0.95,0.96,0.97,0.98,0.99 and each label confidence threshold 0.05,0.10,...,0.95,0.96,0.97,0.98,0.99 pair.", "items": {"$ref": "XPSBoundingBoxMetricsEntry"}, "type": "array"}, "evaluatedBoundingBoxCount": {"description": "The total number of bounding boxes (i.e. summed over all images) the ground truth used to create this evaluation had.", "format": "int32", "type": "integer"}}, "type": "object"}, "XPSImageObjectDetectionModelSpec": {"id": "XPSImageObjectDetectionModelSpec", "properties": {"classCount": {"description": "Total number of classes.", "format": "int64", "type": "string"}, "exportModelSpec": {"$ref": "XPSImageExportModelSpec"}, "maxBoundingBoxCount": {"description": "Max number of bounding box.", "format": "int64", "type": "string"}, "modelArtifactSpec": {"$ref": "XPSImageModelArtifactSpec", "description": "## The fields below are only populated under uCAIP request scope."}, "modelServingSpec": {"$ref": "XPSImageModelServingSpec"}, "stopReason": {"description": "Stop reason for training job, e.g. 'TRAIN_BUDGET_REACHED', 'MODEL_CONVERGED'.", "enum": ["TRAIN_STOP_REASON_UNSPECIFIED", "TRAIN_STOP_REASON_BUDGET_REACHED", "TRAIN_STOP_REASON_MODEL_CONVERGED", "TRAIN_STOP_REASON_MODEL_EARLY_STOPPED"], "enumDescriptions": ["", "", "Model fully converged, can not be resumbed training.", "Model early converged, can be further trained till full convergency."], "type": "string"}, "trainCostNodeSeconds": {"description": "The actual train cost of creating this model, expressed in node seconds, i.e. 3,600 value in this field means 1 node hour.", "format": "int64", "type": "string"}}, "type": "object"}, "XPSImageSegmentationEvaluationMetrics": {"description": "Model evaluation metrics for image segmentation problems. Next tag: 4.", "id": "XPSImageSegmentationEvaluationMetrics", "properties": {"confidenceMetricsEntries": {"description": "Metrics that have confidence thresholds. Precision-recall curve can be derived from it.", "items": {"$ref": "XPSImageSegmentationEvaluationMetricsConfidenceMetricsEntry"}, "type": "array"}}, "type": "object"}, "XPSImageSegmentationEvaluationMetricsConfidenceMetricsEntry": {"description": "Metrics for a single confidence threshold.", "id": "XPSImageSegmentationEvaluationMetricsConfidenceMetricsEntry", "properties": {"confidenceThreshold": {"description": "The confidence threshold value used to compute the metrics.", "format": "float", "type": "number"}, "confusionMatrix": {"$ref": "XPSConfusionMatrix", "description": "Confusion matrix of the per confidence_threshold evaluation. Pixel counts are set here. Only set for model level evaluation, not for evaluation per label."}, "diceScoreCoefficient": {"description": "DSC or the F1 score: The harmonic mean of recall and precision.", "format": "float", "type": "number"}, "iouScore": {"description": "IOU score.", "format": "float", "type": "number"}, "precision": {"description": "Precision for the given confidence threshold.", "format": "float", "type": "number"}, "recall": {"description": "Recall for the given confidence threshold.", "format": "float", "type": "number"}}, "type": "object"}, "XPSImageSegmentationTrainResponse": {"id": "XPSImageSegmentationTrainResponse", "properties": {"colorMaps": {"description": "Color map of the model.", "items": {"$ref": "XPSColorMap"}, "type": "array"}, "exportModelSpec": {"$ref": "XPSImageExportModelSpec", "description": "NOTE: These fields are not used/needed in EAP but will be set later."}, "modelArtifactSpec": {"$ref": "XPSImageModelArtifactSpec", "description": "## The fields below are only populated under uCAIP request scope. Model artifact spec stores and model gcs pathes and related metadata"}, "modelServingSpec": {"$ref": "XPSImageModelServingSpec"}, "stopReason": {"description": "Stop reason for training job, e.g. 'TRAIN_BUDGET_REACHED', 'MODEL_CONVERGED'.", "enum": ["TRAIN_STOP_REASON_UNSPECIFIED", "TRAIN_STOP_REASON_BUDGET_REACHED", "TRAIN_STOP_REASON_MODEL_CONVERGED", "TRAIN_STOP_REASON_MODEL_EARLY_STOPPED"], "enumDescriptions": ["", "", "Model fully converged, can not be resumbed training.", "Model early converged, can be further trained till full convergency."], "type": "string"}, "trainCostNodeSeconds": {"description": "The actual train cost of creating this model, expressed in node seconds, i.e. 3,600 value in this field means 1 node hour.", "format": "int64", "type": "string"}}, "type": "object"}, "XPSIntegratedGradientsAttribution": {"deprecated": true, "description": "An attribution method that computes the <PERSON><PERSON>-<PERSON> value taking advantage of the model's fully differentiable structure. Refer to this paper for more details: https://arxiv.org/abs/1703.01365", "id": "XPSIntegratedGradientsAttribution", "properties": {"stepCount": {"description": "The number of steps for approximating the path integral. A good value to start is 50 and gradually increase until the sum to diff property is within the desired error range. Valid range of its value is [1, 100], inclusively.", "format": "int32", "type": "integer"}}, "type": "object"}, "XPSMetricEntry": {"id": "XPSMetricEntry", "properties": {"argentumMetricId": {"description": "For billing metrics that are using legacy sku's, set the legacy billing metric id here. This will be sent to Chemist as the \"cloudbilling.googleapis.com/argentum_metric_id\" label. Otherwise leave empty.", "type": "string"}, "doubleValue": {"description": "A double value.", "format": "double", "type": "number"}, "int64Value": {"description": "A signed 64-bit integer value.", "format": "int64", "type": "string"}, "metricName": {"description": "The metric name defined in the service configuration.", "type": "string"}, "systemLabels": {"description": "Billing system labels for this (metric, value) pair.", "items": {"$ref": "XPSMetricEntryLabel"}, "type": "array"}}, "type": "object"}, "XPSMetricEntryLabel": {"id": "XPSMetricEntryLabel", "properties": {"labelName": {"description": "The name of the label.", "type": "string"}, "labelValue": {"description": "The value of the label.", "type": "string"}}, "type": "object"}, "XPSModelArtifactItem": {"description": "A single model artifact item.", "id": "XPSModelArtifactItem", "properties": {"artifactFormat": {"description": "The model artifact format.", "enum": ["ARTIFACT_FORMAT_UNSPECIFIED", "TF_CHECKPOINT", "TF_SAVED_MODEL", "TF_LITE", "EDGE_TPU_TF_LITE", "TF_JS", "CORE_ML"], "enumDescriptions": ["Should not be used.", "The Tensorflow checkpoints. See https://www.tensorflow.org/guide/checkpoint.", "The Tensorflow SavedModel binary.", "Model artifact in generic TensorFlow Lite (.tflite) format. See https://www.tensorflow.org/lite.", "Used for [Edge TPU](https://cloud.google.com/edge-tpu/) devices.", "A [TensorFlow.js](https://www.tensorflow.org/js) model that can be used in the browser and in Node.js using JavaScript.", "Used for iOS mobile devices in (.mlmodel) format. See https://developer.apple.com/documentation/coreml"], "type": "string"}, "gcsUri": {"description": "The Google Cloud Storage URI that stores the model binary files.", "type": "string"}}, "type": "object"}, "XPSPreprocessResponse": {"id": "XPSPreprocessResponse", "properties": {"outputExampleSet": {"$ref": "XPSExampleSet", "description": "Preprocessed examples, that are to be imported into AutoML storage. This should point to RecordIO file(s) of PreprocessedExample messages. The PreprocessedExample.mvp_training_data-s returned here are later verbatim passed to Train() call in TrainExample.mvp_training_data."}, "speechPreprocessResp": {"$ref": "XPSSpeechPreprocessResponse"}, "tablesPreprocessResponse": {"$ref": "XPSTablesPreprocessResponse"}, "translationPreprocessResp": {"$ref": "XPSTranslationPreprocessResponse"}}, "type": "object"}, "XPSRegressionEvaluationMetrics": {"description": "Model evaluation metrics for regression problems. It can be used for Tables.", "id": "XPSRegressionEvaluationMetrics", "properties": {"meanAbsoluteError": {"description": "Mean Absolute Error (MAE).", "format": "float", "type": "number"}, "meanAbsolutePercentageError": {"description": "Mean absolute percentage error. Only set if all ground truth values are positive.", "format": "float", "type": "number"}, "rSquared": {"description": "R squared.", "format": "float", "type": "number"}, "regressionMetricsEntries": {"description": "A list of actual versus predicted points for the model being evaluated.", "items": {"$ref": "XPSRegressionMetricsEntry"}, "type": "array"}, "rootMeanSquaredError": {"description": "Root Mean Squared Error (RMSE).", "format": "float", "type": "number"}, "rootMeanSquaredLogError": {"description": "Root mean squared log error.", "format": "float", "type": "number"}}, "type": "object"}, "XPSRegressionMetricsEntry": {"description": "A pair of actual & observed values for the model being evaluated.", "id": "XPSRegressionMetricsEntry", "properties": {"predictedValue": {"description": "The observed value for a row in the dataset.", "format": "float", "type": "number"}, "trueValue": {"description": "The actual target value for a row in the dataset.", "format": "float", "type": "number"}}, "type": "object"}, "XPSReportingMetrics": {"id": "XPSReportingMetrics", "properties": {"effectiveTrainingDuration": {"deprecated": true, "description": "The effective time training used. If set, this is used for quota management and billing. Deprecated. AutoML BE doesn't use this. Don't set.", "format": "google-duration", "type": "string"}, "metricEntries": {"description": "One entry per metric name. The values must be aggregated per metric name.", "items": {"$ref": "XPSMetricEntry"}, "type": "array"}}, "type": "object"}, "XPSResponseExplanationMetadata": {"deprecated": true, "id": "XPSResponseExplanationMetadata", "properties": {"inputs": {"additionalProperties": {"$ref": "XPSResponseExplanationMetadataInputMetadata"}, "description": "<PERSON><PERSON><PERSON> of the input.", "type": "object"}, "outputs": {"additionalProperties": {"$ref": "XPSResponseExplanationMetadataOutputMetadata"}, "description": "Metadata of the output.", "type": "object"}}, "type": "object"}, "XPSResponseExplanationMetadataInputMetadata": {"description": "Metadata of the input of a feature.", "id": "XPSResponseExplanationMetadataInputMetadata", "properties": {"inputTensorName": {"description": "Name of the input tensor for this model. Only needed in train response.", "type": "string"}, "modality": {"description": "Modality of the feature. Valid values are: numeric, image. Defaults to numeric.", "enum": ["MODALITY_UNSPECIFIED", "NUMERIC", "IMAGE", "CATEGORICAL"], "enumDescriptions": ["", "", "", ""], "type": "string"}, "visualizationConfig": {"$ref": "XPSVisualization", "description": "Visualization configurations for image explanation."}}, "type": "object"}, "XPSResponseExplanationMetadataOutputMetadata": {"description": "Metadata of the prediction output to be explained.", "id": "XPSResponseExplanationMetadataOutputMetadata", "properties": {"outputTensorName": {"description": "Name of the output tensor. Only needed in train response.", "type": "string"}}, "type": "object"}, "XPSResponseExplanationParameters": {"deprecated": true, "id": "XPSResponseExplanationParameters", "properties": {"integratedGradientsAttribution": {"$ref": "XPSIntegratedGradientsAttribution", "description": "An attribution method that computes <PERSON><PERSON> values taking advantage of the model's fully differentiable structure. Refer to this paper for more details: https://arxiv.org/abs/1703.01365"}, "xraiAttribution": {"$ref": "XPSXraiAttribution", "description": "An attribution method that redistributes Integrated Gradients attribution to segmented regions, taking advantage of the model's fully differentiable structure. Refer to this paper for more details: https://arxiv.org/abs/1906.02825 XRAI currently performs better on natural images, like a picture of a house or an animal. If the images are taken in artificial environments, like a lab or manufacturing line, or from diagnostic equipment, like x-rays or quality-control cameras, use Integrated Gradients instead."}}, "type": "object"}, "XPSResponseExplanationSpec": {"deprecated": true, "description": "Specification of Model explanation. Feature-based XAI in AutoML Vision ICN is deprecated.", "id": "XPSResponseExplanationSpec", "properties": {"explanationType": {"description": "Explanation type. For AutoML Image Classification models, possible values are: * `image-integrated-gradients` * `image-xrai`", "type": "string"}, "metadata": {"$ref": "XPSResponseExplanationMetadata", "description": "Metadata describing the Model's input and output for explanation."}, "parameters": {"$ref": "XPSResponseExplanationParameters", "description": "Parameters that configure explaining of the Model's predictions."}}, "type": "object"}, "XPSRow": {"id": "XPSRow", "properties": {"columnIds": {"description": "The ids of the columns. Note: The below `values` field must match order of this field, if this field is set.", "items": {"format": "int32", "type": "integer"}, "type": "array"}, "values": {"description": "The values of the row cells, given in the same order as the column_ids. If column_ids is not set, then in the same order as the input_feature_column_ids in TablesModelMetadata.", "items": {"type": "any"}, "type": "array"}}, "type": "object"}, "XPSSpeechEvaluationMetrics": {"id": "XPSSpeechEvaluationMetrics", "properties": {"subModelEvaluationMetrics": {"description": "Evaluation metrics for all submodels contained in this model.", "items": {"$ref": "XPSSpeechEvaluationMetricsSubModelEvaluationMetric"}, "type": "array"}}, "type": "object"}, "XPSSpeechEvaluationMetricsSubModelEvaluationMetric": {"id": "XPSSpeechEvaluationMetricsSubModelEvaluationMetric", "properties": {"biasingModelType": {"description": "Type of the biasing model.", "enum": ["BIASING_MODEL_TYPE_UNSPECIFIED", "COMMAND_AND_SEARCH", "PHONE_CALL", "VIDEO", "DEFAULT"], "enumDescriptions": ["", "Build biasing model on top of COMMAND_AND_SEARCH model", "Build biasing model on top of PHONE_CALL model", "Build biasing model on top of VIDEO model", "Build biasing model on top of DEFAULT model"], "type": "string"}, "isEnhancedModel": {"description": "If true then it means we have an enhanced version of the biasing models.", "type": "boolean"}, "numDeletions": {"format": "int32", "type": "integer"}, "numInsertions": {"format": "int32", "type": "integer"}, "numSubstitutions": {"format": "int32", "type": "integer"}, "numUtterances": {"description": "Number of utterances used in the wer computation.", "format": "int32", "type": "integer"}, "numWords": {"description": "Number of words over which the word error rate was computed.", "format": "int32", "type": "integer"}, "sentenceAccuracy": {"description": "Below fields are used for debugging purposes", "format": "double", "type": "number"}, "wer": {"description": "Word error rate (standard error metric used for speech recognition).", "format": "double", "type": "number"}}, "type": "object"}, "XPSSpeechModelSpec": {"id": "XPSSpeechModelSpec", "properties": {"datasetId": {"description": "Required for speech xps backend. Speech xps has to use dataset_id and model_id as the primary key in db so that speech API can query the db directly.", "format": "int64", "type": "string"}, "language": {"type": "string"}, "subModelSpecs": {"description": "Model specs for all submodels contained in this model.", "items": {"$ref": "XPSSpeechModelSpecSubModelSpec"}, "type": "array"}}, "type": "object"}, "XPSSpeechModelSpecSubModelSpec": {"id": "XPSSpeechModelSpecSubModelSpec", "properties": {"biasingModelType": {"description": "Type of the biasing model.", "enum": ["BIASING_MODEL_TYPE_UNSPECIFIED", "COMMAND_AND_SEARCH", "PHONE_CALL", "VIDEO", "DEFAULT"], "enumDescriptions": ["", "Build biasing model on top of COMMAND_AND_SEARCH model", "Build biasing model on top of PHONE_CALL model", "Build biasing model on top of VIDEO model", "Build biasing model on top of DEFAULT model"], "type": "string"}, "clientId": {"description": "In S3, Recognition ClientContextId.client_id", "type": "string"}, "contextId": {"description": "In S3, Recognition ClientContextId.context_id", "type": "string"}, "isEnhancedModel": {"description": "If true then it means we have an enhanced version of the biasing models.", "type": "boolean"}}, "type": "object"}, "XPSSpeechPreprocessResponse": {"id": "XPSSpeechPreprocessResponse", "properties": {"cnsTestDataPath": {"description": "Location od shards of sstables (test data) of DataUtterance protos.", "type": "string"}, "cnsTrainDataPath": {"description": "Location of shards of sstables (training data) of DataUtterance protos.", "type": "string"}, "prebuiltModelEvaluationMetrics": {"$ref": "XPSSpeechEvaluationMetrics", "description": "The metrics for prebuilt speech models. They are included here because there is no prebuilt speech models stored in the AutoML."}, "speechPreprocessStats": {"$ref": "XPSSpeechPreprocessStats", "description": "Stats associated with the data."}}, "type": "object"}, "XPSSpeechPreprocessStats": {"id": "XPSSpeechPreprocessStats", "properties": {"dataErrors": {"description": "Different types of data errors and the counts associated with them.", "items": {"$ref": "XPSDataErrors"}, "type": "array"}, "numHumanLabeledExamples": {"description": "The number of rows marked HUMAN_LABELLED", "format": "int32", "type": "integer"}, "numLogsExamples": {"description": "The number of samples found in the previously recorded logs data.", "format": "int32", "type": "integer"}, "numMachineTranscribedExamples": {"description": "The number of rows marked as MACHINE_TRANSCRIBED", "format": "int32", "type": "integer"}, "testExamplesCount": {"description": "The number of examples labelled as TEST by Speech xps server.", "format": "int32", "type": "integer"}, "testSentencesCount": {"description": "The number of sentences in the test data set.", "format": "int32", "type": "integer"}, "testWordsCount": {"description": "The number of words in the test data set.", "format": "int32", "type": "integer"}, "trainExamplesCount": {"description": "The number of examples labeled as TRAIN by Speech xps server.", "format": "int32", "type": "integer"}, "trainSentencesCount": {"description": "The number of sentences in the training data set.", "format": "int32", "type": "integer"}, "trainWordsCount": {"description": "The number of words in the training data set.", "format": "int32", "type": "integer"}}, "type": "object"}, "XPSStringStats": {"description": "The data statistics of a series of STRING values.", "id": "XPSStringStats", "properties": {"commonStats": {"$ref": "XPSCommonStats"}, "topUnigramStats": {"description": "The statistics of the top 20 unigrams, ordered by StringStats.UnigramStats.count.", "items": {"$ref": "XPSStringStatsUnigramStats"}, "type": "array"}}, "type": "object"}, "XPSStringStatsUnigramStats": {"description": "The statistics of a unigram.", "id": "XPSStringStatsUnigramStats", "properties": {"count": {"description": "The number of occurrences of this unigram in the series.", "format": "int64", "type": "string"}, "value": {"description": "The unigram.", "type": "string"}}, "type": "object"}, "XPSStructStats": {"description": "The data statistics of a series of STRUCT values.", "id": "XPSStructStats", "properties": {"commonStats": {"$ref": "XPSCommonStats"}, "fieldStats": {"additionalProperties": {"$ref": "XPSDataStats"}, "description": "Map from a field name of the struct to data stats aggregated over series of all data in that field across all the structs.", "type": "object"}}, "type": "object"}, "XPSStructType": {"description": "`StructType` defines the DataType-s of a STRUCT type.", "id": "XPSStructType", "properties": {"fields": {"additionalProperties": {"$ref": "XPSDataType"}, "description": "Unordered map of struct field names to their data types.", "type": "object"}}, "type": "object"}, "XPSTableSpec": {"id": "XPSTableSpec", "properties": {"columnSpecs": {"additionalProperties": {"$ref": "XPSColumnSpec"}, "description": "Mapping from column id to column spec.", "type": "object"}, "importedDataSizeInBytes": {"description": "The total size of imported data of the table.", "format": "int64", "type": "string"}, "rowCount": {"description": "The number of rows in the table.", "format": "int64", "type": "string"}, "timeColumnId": {"description": "The id of the time column.", "format": "int32", "type": "integer"}, "validRowCount": {"description": "The number of valid rows.", "format": "int64", "type": "string"}}, "type": "object"}, "XPSTablesClassificationMetrics": {"description": "Metrics for Tables classification problems.", "id": "XPSTablesClassificationMetrics", "properties": {"curveMetrics": {"description": "Metrics building a curve.", "items": {"$ref": "XPSTablesClassificationMetricsCurveMetrics"}, "type": "array"}}, "type": "object"}, "XPSTablesClassificationMetricsCurveMetrics": {"description": "Metrics curve data point for a single value.", "id": "XPSTablesClassificationMetricsCurveMetrics", "properties": {"aucPr": {"description": "The area under the precision-recall curve.", "format": "double", "type": "number"}, "aucRoc": {"description": "The area under receiver operating characteristic curve.", "format": "double", "type": "number"}, "confidenceMetricsEntries": {"description": "Metrics that have confidence thresholds. Precision-recall curve and ROC curve can be derived from them.", "items": {"$ref": "XPSTablesConfidenceMetricsEntry"}, "type": "array"}, "logLoss": {"description": "The Log loss metric.", "format": "double", "type": "number"}, "positionThreshold": {"description": "The position threshold value used to compute the metrics.", "format": "int32", "type": "integer"}, "value": {"description": "The CATEGORY row value (for ARRAY unnested) the curve metrics are for.", "type": "string"}}, "type": "object"}, "XPSTablesConfidenceMetricsEntry": {"description": "Metrics for a single confidence threshold.", "id": "XPSTablesConfidenceMetricsEntry", "properties": {"confidenceThreshold": {"description": "The confidence threshold value used to compute the metrics.", "format": "double", "type": "number"}, "f1Score": {"description": "The harmonic mean of recall and precision. (2 * precision * recall) / (precision + recall)", "format": "double", "type": "number"}, "falseNegativeCount": {"description": "False negative count.", "format": "int64", "type": "string"}, "falsePositiveCount": {"description": "False positive count.", "format": "int64", "type": "string"}, "falsePositiveRate": {"description": "FPR = #false positives / (#false positives + #true negatives)", "format": "double", "type": "number"}, "precision": {"description": "Precision = #true positives / (#true positives + #false positives).", "format": "double", "type": "number"}, "recall": {"description": "Recall = #true positives / (#true positives + #false negatives).", "format": "double", "type": "number"}, "trueNegativeCount": {"description": "True negative count.", "format": "int64", "type": "string"}, "truePositiveCount": {"description": "True positive count.", "format": "int64", "type": "string"}, "truePositiveRate": {"description": "TPR = #true positives / (#true positives + #false negatvies)", "format": "double", "type": "number"}}, "type": "object"}, "XPSTablesDatasetMetadata": {"description": "Metadata for a dataset used for AutoML Tables.", "id": "XPSTablesDatasetMetadata", "properties": {"mlUseColumnId": {"description": "Id the column to split the table.", "format": "int32", "type": "integer"}, "primaryTableSpec": {"$ref": "XPSTableSpec", "description": "Primary table."}, "targetColumnCorrelations": {"additionalProperties": {"$ref": "XPSCorrelationStats"}, "description": "(the column id : its CorrelationStats with target column).", "type": "object"}, "targetColumnId": {"description": "Id of the primary table column that should be used as the training label.", "format": "int32", "type": "integer"}, "weightColumnId": {"description": "Id of the primary table column that should be used as the weight column.", "format": "int32", "type": "integer"}}, "type": "object"}, "XPSTablesEvaluationMetrics": {"id": "XPSTablesEvaluationMetrics", "properties": {"classificationMetrics": {"$ref": "XPSTablesClassificationMetrics", "description": "Classification metrics."}, "regressionMetrics": {"$ref": "XPSTablesRegressionMetrics", "description": "Regression metrics."}}, "type": "object"}, "XPSTablesModelColumnInfo": {"description": "An information specific to given column and Tables Model, in context of the Model and the predictions created by it.", "id": "XPSTablesModelColumnInfo", "properties": {"columnId": {"description": "The ID of the column.", "format": "int32", "type": "integer"}, "featureImportance": {"description": "When given as part of a Model: Measurement of how much model predictions correctness on the TEST data depend on values in this column. A value between 0 and 1, higher means higher influence. These values are normalized - for all input feature columns of a given model they add to 1. When given back by <PERSON><PERSON> or <PERSON><PERSON>dict: Measurement of how impactful for the prediction returned for the given row the value in this column was. Specifically, the feature importance specifies the marginal contribution that the feature made to the prediction score compared to the baseline score. These values are computed using the Sampled S<PERSON>y method.", "format": "float", "type": "number"}}, "type": "object"}, "XPSTablesModelStructure": {"description": "A description of Tables model structure.", "id": "XPSTablesModelStructure", "properties": {"modelParameters": {"description": "A list of models.", "items": {"$ref": "XPSTablesModelStructureModelParameters"}, "type": "array"}}, "type": "object"}, "XPSTablesModelStructureModelParameters": {"description": "Model hyper-parameters for a model.", "id": "XPSTablesModelStructureModelParameters", "properties": {"hyperparameters": {"items": {"$ref": "XPSTablesModelStructureModelParametersParameter"}, "type": "array"}}, "type": "object"}, "XPSTablesModelStructureModelParametersParameter": {"id": "XPSTablesModelStructureModelParametersParameter", "properties": {"floatValue": {"description": "Float type parameter value.", "format": "double", "type": "number"}, "intValue": {"description": "Integer type parameter value.", "format": "int64", "type": "string"}, "name": {"description": "Parameter name.", "type": "string"}, "stringValue": {"description": "String type parameter value.", "type": "string"}}, "type": "object"}, "XPSTablesPreprocessResponse": {"id": "XPSTablesPreprocessResponse", "properties": {"tablesDatasetMetadata": {"$ref": "XPSTablesDatasetMetadata", "description": "The table/column id, column_name and the DataTypes of the columns will be populated."}}, "type": "object"}, "XPSTablesRegressionMetrics": {"description": "Metrics for Tables regression problems.", "id": "XPSTablesRegressionMetrics", "properties": {"meanAbsoluteError": {"description": "Mean absolute error.", "format": "double", "type": "number"}, "meanAbsolutePercentageError": {"description": "Mean absolute percentage error, only set if all of the target column's values are positive.", "format": "double", "type": "number"}, "rSquared": {"description": "R squared.", "format": "double", "type": "number"}, "regressionMetricsEntries": {"description": "A list of actual versus predicted points for the model being evaluated.", "items": {"$ref": "XPSRegressionMetricsEntry"}, "type": "array"}, "rootMeanSquaredError": {"description": "Root mean squared error.", "format": "double", "type": "number"}, "rootMeanSquaredLogError": {"description": "Root mean squared log error.", "format": "double", "type": "number"}}, "type": "object"}, "XPSTablesTrainResponse": {"id": "XPSTablesTrainResponse", "properties": {"modelStructure": {"$ref": "XPSTablesModelStructure"}, "predictionSampleRows": {"description": "Sample rows from the dataset this model was trained.", "items": {"$ref": "XPSRow"}, "type": "array"}, "tablesModelColumnInfo": {"description": "Output only. Auxiliary information for each of the input_feature_column_specs, with respect to this particular model.", "items": {"$ref": "XPSTablesModelColumnInfo"}, "type": "array"}, "trainCostMilliNodeHours": {"description": "The actual training cost of the model, expressed in milli node hours, i.e. 1,000 value in this field means 1 node hour. Guaranteed to not exceed the train budget.", "format": "int64", "type": "string"}}, "type": "object"}, "XPSTablesTrainingOperationMetadata": {"id": "XPSTablesTrainingOperationMetadata", "properties": {"createModelStage": {"description": "Current stage of creating model.", "enum": ["CREATE_MODEL_STAGE_UNSPECIFIED", "DATA_PREPROCESSING", "TRAINING", "EVALUATING", "MODEL_POST_PROCESSING"], "enumDescriptions": ["Unspecified stage.", "Prepare the model training pipeline and run data processing.", "Training model.", "Run evaluation.", "Finalizing model training pipeline."], "type": "string"}, "optimizationObjective": {"description": "The optimization objective for model.", "type": "string"}, "topTrials": {"description": "This field is for training. When the operation is terminated successfully, AutoML Backend post this field to operation metadata in spanner. If the metadata has no trials returned, the training operation is supposed to be a failure.", "items": {"$ref": "XPSTuningTrial"}, "type": "array"}, "trainBudgetMilliNodeHours": {"description": "Creating model budget.", "format": "int64", "type": "string"}, "trainingObjectivePoints": {"description": "This field records the training objective value with respect to time, giving insight into how the model architecture search is performing as training time elapses.", "items": {"$ref": "XPSTrainingObjectivePoint"}, "type": "array"}, "trainingStartTime": {"description": "Timestamp when training process starts.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "XPSTextComponentModel": {"description": "Component model.", "id": "XPSTextComponentModel", "properties": {"batchPredictionModelGcsUri": {"description": "The Cloud Storage resource path to hold batch prediction model.", "type": "string"}, "onlinePredictionModelGcsUri": {"description": "The Cloud Storage resource path to hold online prediction model.", "type": "string"}, "partition": {"description": "The partition where the model is deployed. Populated by uCAIP BE as part of online PredictRequest.", "enum": ["PARTITION_TYPE_UNSPECIFIED", "PARTITION_ZERO", "PARTITION_REDUCED_HOMING", "PARTITION_JELLYFISH", "PARTITION_CPU", "PARTITION_CUSTOM_STORAGE_CPU"], "enumDescriptions": ["", "The default partition.", "It has significantly lower replication than partition-0 and is located in the US only. It also has a larger model size limit and higher default RAM quota than partition-0. Customers with batch traffic, US-based traffic, or very large models should use this partition. Capacity in this partition is significantly cheaper than partition-0.", "To be used by customers with Jellyfish-accelerated ops.", "The partition used by regionalized servomatic cloud regions.", "The partition used for loading models from custom storage."], "type": "string"}, "servingArtifact": {"$ref": "XPSModelArtifactItem", "description": "The default model binary file used for serving (e.g. online predict, batch predict) via public Cloud Ai Platform API."}, "servoModelName": {"description": "The name of servo model. Populated by uCAIP BE as part of online PredictRequest.", "type": "string"}, "submodelName": {"description": "The name of the trained NL submodel.", "type": "string"}, "submodelType": {"description": "The type of trained NL submodel", "enum": ["TEXT_MODEL_TYPE_UNSPECIFIED", "TEXT_MODEL_TYPE_DEFAULT", "TEXT_MODEL_TYPE_META_ARCHITECT", "TEXT_MODEL_TYPE_ATC", "TEXT_MODEL_TYPE_CLARA2", "TEXT_MODEL_TYPE_CHATBASE", "TEXT_MODEL_TYPE_SAFT_SPAN_LABELING", "TEXT_MODEL_TYPE_TEXT_EXTRACTION", "TEXT_MODEL_TYPE_RELATIONSHIP_EXTRACTION", "TEXT_MODEL_TYPE_COMPOSITE", "TEXT_MODEL_TYPE_ALL_MODELS", "TEXT_MODEL_TYPE_BERT", "TEXT_MODEL_TYPE_ENC_PALM"], "enumDescriptions": ["", "", "", "", "", "", "", "Model type for entity extraction.", "Model type for relationship extraction.", "A composite model represents a set of component models that have to be used together for prediction. A composite model appears to be a single model to the model user. It may contain only one component model.", "Model type used to train default, MA, and ATC models in a single batch worker pipeline.", "BERT pipeline needs a specific model type, since it uses a different TFX configuration compared with DEFAULT (despite sharing most of the code).", "Model type for EncPaLM."], "type": "string"}, "tfRuntimeVersion": {"description": "## The fields below are only populated under uCAIP request scope. https://cloud.google.com/ml-engine/docs/runtime-version-list", "type": "string"}, "versionNumber": {"description": "The servomatic model version number. Populated by uCAIP BE as part of online PredictRequest.", "format": "int64", "type": "string"}}, "type": "object"}, "XPSTextExtractionEvaluationMetrics": {"id": "XPSTextExtractionEvaluationMetrics", "properties": {"bestF1ConfidenceMetrics": {"$ref": "XPSConfidenceMetricsEntry", "deprecated": true, "description": "Values are at the highest F1 score on the precision-recall curve. Only confidence_threshold, recall, precision, and f1_score will be set."}, "confidenceMetricsEntries": {"description": "If the enclosing EvaluationMetrics.label is empty, confidence_metrics_entries is an evaluation of the entire model across all labels. If the enclosing EvaluationMetrics.label is set, confidence_metrics_entries applies to that label.", "items": {"$ref": "XPSConfidenceMetricsEntry"}, "type": "array"}, "confusionMatrix": {"$ref": "XPSConfusionMatrix", "description": "Confusion matrix of the model, at the default confidence threshold (0.0). Only set for whole-model evaluation, not for evaluation per label."}, "perLabelConfidenceMetrics": {"additionalProperties": {"$ref": "XPSConfidenceMetricsEntry"}, "deprecated": true, "description": "Only recall, precision, and f1_score will be set.", "type": "object"}}, "type": "object"}, "XPSTextSentimentEvaluationMetrics": {"description": "Model evaluation metrics for text sentiment problems.", "id": "XPSTextSentimentEvaluationMetrics", "properties": {"confusionMatrix": {"$ref": "XPSConfusionMatrix", "description": "Output only. Confusion matrix of the evaluation. Only set for the overall model evaluation, not for evaluation of a single annotation spec."}, "f1Score": {"description": "Output only. The harmonic mean of recall and precision.", "format": "float", "type": "number"}, "linearKappa": {"description": "Output only. Linear weighted kappa. Only set for the overall model evaluation, not for evaluation of a single annotation spec.", "format": "float", "type": "number"}, "meanAbsoluteError": {"description": "Output only. Mean absolute error. Only set for the overall model evaluation, not for evaluation of a single annotation spec.", "format": "float", "type": "number"}, "meanSquaredError": {"description": "Output only. Mean squared error. Only set for the overall model evaluation, not for evaluation of a single annotation spec.", "format": "float", "type": "number"}, "precision": {"description": "Output only. Precision.", "format": "float", "type": "number"}, "quadraticKappa": {"description": "Output only. Quadratic weighted kappa. Only set for the overall model evaluation, not for evaluation of a single annotation spec.", "format": "float", "type": "number"}, "recall": {"description": "Output only. Recall.", "format": "float", "type": "number"}}, "type": "object"}, "XPSTextToSpeechTrainResponse": {"description": "TextToSpeech train response", "id": "XPSTextToSpeechTrainResponse", "properties": {}, "type": "object"}, "XPSTextTrainResponse": {"id": "XPSTextTrainResponse", "properties": {"componentModel": {"description": "Component submodels.", "items": {"$ref": "XPSTextComponentModel"}, "type": "array"}}, "type": "object"}, "XPSTfJsFormat": {"description": "A [TensorFlow.js](https://www.tensorflow.org/js) model that can be used in the browser and in Node.js using JavaScript.", "id": "XPSTfJsFormat", "properties": {}, "type": "object"}, "XPSTfLiteFormat": {"description": "LINT.IfChange A model format used for mobile and IoT devices. See https://www.tensorflow.org/lite.", "id": "XPSTfLiteFormat", "properties": {}, "type": "object"}, "XPSTfSavedModelFormat": {"description": "A tensorflow model format in SavedModel format.", "id": "XPSTfSavedModelFormat", "properties": {}, "type": "object"}, "XPSTimestampStats": {"description": "The data statistics of a series of TIMESTAMP values.", "id": "XPSTimestampStats", "properties": {"commonStats": {"$ref": "XPSCommonStats"}, "granularStats": {"additionalProperties": {"$ref": "XPSTimestampStatsGranularStats"}, "description": "The string key is the pre-defined granularity. Currently supported: hour_of_day, day_of_week, month_of_year. Granularities finer that the granularity of timestamp data are not populated (e.g. if timestamps are at day granularity, then hour_of_day is not populated).", "type": "object"}, "medianTimestampNanos": {"format": "int64", "type": "string"}}, "type": "object"}, "XPSTimestampStatsGranularStats": {"description": "Stats split by a defined in context granularity.", "id": "XPSTimestampStatsGranularStats", "properties": {"buckets": {"additionalProperties": {"format": "int64", "type": "string"}, "description": "A map from granularity key to example count for that key. E.g. for hour_of_day `13` means 1pm, or for month_of_year `5` means May).", "type": "object"}}, "type": "object"}, "XPSTrackMetricsEntry": {"description": "Track matching model metrics for a single track match threshold and multiple label match confidence thresholds. Next tag: 6.", "id": "XPSTrackMetricsEntry", "properties": {"confidenceMetricsEntries": {"description": "Output only. Metrics for each label-match confidence_threshold from 0.05,0.10,...,0.95,0.96,0.97,0.98,0.99. Precision-recall curve is derived from them.", "items": {"$ref": "XPSTrackMetricsEntryConfidenceMetricsEntry"}, "type": "array"}, "iouThreshold": {"description": "Output only. The intersection-over-union threshold value between bounding boxes across frames used to compute this metric entry.", "format": "float", "type": "number"}, "meanBoundingBoxIou": {"description": "Output only. The mean bounding box iou over all confidence thresholds.", "format": "float", "type": "number"}, "meanMismatchRate": {"description": "Output only. The mean mismatch rate over all confidence thresholds.", "format": "float", "type": "number"}, "meanTrackingAveragePrecision": {"description": "Output only. The mean average precision over all confidence thresholds.", "format": "float", "type": "number"}}, "type": "object"}, "XPSTrackMetricsEntryConfidenceMetricsEntry": {"description": "Metrics for a single confidence threshold. Next tag: 6.", "id": "XPSTrackMetricsEntryConfidenceMetricsEntry", "properties": {"boundingBoxIou": {"description": "Output only. Bounding box intersection-over-union precision. Measures how well the bounding boxes overlap between each other (e.g. complete overlap or just barely above iou_threshold).", "format": "float", "type": "number"}, "confidenceThreshold": {"description": "Output only. The confidence threshold value used to compute the metrics.", "format": "float", "type": "number"}, "mismatchRate": {"description": "Output only. Mismatch rate, which measures the tracking consistency, i.e. correctness of instance ID continuity.", "format": "float", "type": "number"}, "trackingPrecision": {"description": "Output only. Tracking precision.", "format": "float", "type": "number"}, "trackingRecall": {"description": "Output only. Tracking recall.", "format": "float", "type": "number"}}, "type": "object"}, "XPSTrainResponse": {"id": "XPSTrainResponse", "properties": {"deployedModelSizeBytes": {"description": "Estimated model size in bytes once deployed.", "format": "int64", "type": "string"}, "errorAnalysisConfigs": {"description": "Optional vision model error analysis configuration. The field is set when model error analysis is enabled in the training request. The results of error analysis will be binded together with evaluation results (in the format of AnnotatedExample).", "items": {"$ref": "XPSVisionErrorAnalysisConfig"}, "type": "array"}, "evaluatedExampleSet": {"$ref": "XPSExampleSet", "description": "Examples used to evaluate the model (usually the test set), with the predicted annotations. The file_spec should point to recordio file(s) of AnnotatedExample. For each returned example, the example_id_token and annotations predicted by the model must be set. The example payload can and is recommended to be omitted."}, "evaluationMetricsSet": {"$ref": "XPSEvaluationMetricsSet", "description": "The trained model evaluation metrics. This can be optionally returned."}, "explanationConfigs": {"deprecated": true, "description": "VisionExplanationConfig for XAI on test set. Optional for when XAI is enable in training request.", "items": {"$ref": "XPSResponseExplanationSpec"}, "type": "array"}, "imageClassificationTrainResp": {"$ref": "XPSImageClassificationTrainResponse"}, "imageObjectDetectionTrainResp": {"$ref": "XPSImageObjectDetectionModelSpec"}, "imageSegmentationTrainResp": {"$ref": "XPSImageSegmentationTrainResponse"}, "modelToken": {"description": "Token that represents the trained model. This is considered immutable and is persisted in AutoML. xPS can put their own proto in the byte string, to e.g. point to the model checkpoints. The token is passed to other xPS APIs to refer to the model.", "format": "byte", "type": "string"}, "speechTrainResp": {"$ref": "XPSSpeechModelSpec"}, "tablesTrainResp": {"$ref": "XPSTablesTrainResponse"}, "textToSpeechTrainResp": {"$ref": "XPSTextToSpeechTrainResponse"}, "textTrainResp": {"$ref": "XPSTextTrainResponse", "description": "Will only be needed for uCAIP from Beta."}, "translationTrainResp": {"$ref": "XPSTranslationTrainResponse"}, "videoActionRecognitionTrainResp": {"$ref": "XPSVideoActionRecognitionTrainResponse"}, "videoClassificationTrainResp": {"$ref": "XPSVideoClassificationTrainResponse"}, "videoObjectTrackingTrainResp": {"$ref": "XPSVideoObjectTrackingTrainResponse"}}, "type": "object"}, "XPSTrainingObjectivePoint": {"id": "XPSTrainingObjectivePoint", "properties": {"createTime": {"description": "The time at which this point was recorded.", "format": "google-datetime", "type": "string"}, "value": {"description": "The objective value when this point was recorded.", "format": "float", "type": "number"}}, "type": "object"}, "XPSTranslationEvaluationMetrics": {"description": "Evaluation metrics for the dataset.", "id": "XPSTranslationEvaluationMetrics", "properties": {"baseBleuScore": {"description": "BLEU score for base model.", "format": "double", "type": "number"}, "bleuScore": {"description": "BLEU score.", "format": "double", "type": "number"}}, "type": "object"}, "XPSTranslationPreprocessResponse": {"description": "Translation preprocess response.", "id": "XPSTranslationPreprocessResponse", "properties": {"parsedExampleCount": {"description": "Total example count parsed.", "format": "int64", "type": "string"}, "validExampleCount": {"description": "Total valid example count.", "format": "int64", "type": "string"}}, "type": "object"}, "XPSTranslationTrainResponse": {"description": "Train response for translation.", "id": "XPSTranslationTrainResponse", "properties": {"modelType": {"description": "Type of the model.", "enum": ["MODEL_TYPE_UNSPECIFIED", "LEGACY", "CURRENT"], "enumDescriptions": ["<PERSON><PERSON><PERSON>", "Legacy model. Will be deprecated.", "Current model."], "type": "string"}}, "type": "object"}, "XPSTuningTrial": {"description": "Metrics for a tuning job generated, will get forwarded to Stackdriver as model tuning logs. Setting this as a standalone message out of CreateModelMetadata to avoid confusion as we expose this message only to users.", "id": "XPSTuningTrial", "properties": {"modelStructure": {"$ref": "XPSTablesModelStructure", "description": "Model parameters for the trial."}, "trainingObjectivePoint": {"$ref": "XPSTrainingObjectivePoint", "description": "The optimization objective evaluation of the eval split data."}}, "type": "object"}, "XPSVideoActionMetricsEntry": {"description": "The Evaluation metrics entry given a specific precision_window_length.", "id": "XPSVideoActionMetricsEntry", "properties": {"confidenceMetricsEntries": {"description": "Metrics for each label-match confidence_threshold from 0.05,0.10,...,0.95,0.96,0.97,0.98,0.99.", "items": {"$ref": "XPSVideoActionMetricsEntryConfidenceMetricsEntry"}, "type": "array"}, "meanAveragePrecision": {"description": "The mean average precision.", "format": "float", "type": "number"}, "precisionWindowLength": {"description": "This VideoActionMetricsEntry is calculated based on this prediction window length. If the predicted action's timestamp is inside the time window whose center is the ground truth action's timestamp with this specific length, the prediction result is treated as a true positive.", "format": "google-duration", "type": "string"}}, "type": "object"}, "XPSVideoActionMetricsEntryConfidenceMetricsEntry": {"description": "Metrics for a single confidence threshold.", "id": "XPSVideoActionMetricsEntryConfidenceMetricsEntry", "properties": {"confidenceThreshold": {"description": "Output only. The confidence threshold value used to compute the metrics.", "format": "float", "type": "number"}, "f1Score": {"description": "Output only. The harmonic mean of recall and precision.", "format": "float", "type": "number"}, "precision": {"description": "Output only. Precision for the given confidence threshold.", "format": "float", "type": "number"}, "recall": {"description": "Output only. Recall for the given confidence threshold.", "format": "float", "type": "number"}}, "type": "object"}, "XPSVideoActionRecognitionEvaluationMetrics": {"description": "Model evaluation metrics for video action recognition.", "id": "XPSVideoActionRecognitionEvaluationMetrics", "properties": {"evaluatedActionCount": {"description": "Output only. The number of ground truth actions used to create this evaluation.", "format": "int32", "type": "integer"}, "videoActionMetricsEntries": {"description": "Output only. The metric entries for precision window lengths: 1s,2s,3s,4s, 5s.", "items": {"$ref": "XPSVideoActionMetricsEntry"}, "type": "array"}}, "type": "object"}, "XPSVideoActionRecognitionTrainResponse": {"id": "XPSVideoActionRecognitionTrainResponse", "properties": {"modelArtifactSpec": {"$ref": "XPSVideoModelArtifactSpec", "description": "## The fields below are only populated under uCAIP request scope."}, "trainCostNodeSeconds": {"description": "The actual train cost of creating this model, expressed in node seconds, i.e. 3,600 value in this field means 1 node hour.", "format": "int64", "type": "string"}}, "type": "object"}, "XPSVideoBatchPredictOperationMetadata": {"id": "XPSVideoBatchPredictOperationMetadata", "properties": {"outputExamples": {"description": "All the partial batch prediction results that are completed at the moment. Output examples are sorted by completion time. The order will not be changed. Each output example should be the path of a single RecordIO file of AnnotatedExamples.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "XPSVideoClassificationTrainResponse": {"id": "XPSVideoClassificationTrainResponse", "properties": {"modelArtifactSpec": {"$ref": "XPSVideoModelArtifactSpec", "description": "## The fields below are only populated under uCAIP request scope."}, "trainCostNodeSeconds": {"description": "The actual train cost of creating this model, expressed in node seconds, i.e. 3,600 value in this field means 1 node hour.", "format": "int64", "type": "string"}}, "type": "object"}, "XPSVideoExportModelSpec": {"description": "Information of downloadable models that are pre-generated as part of training flow and will be persisted in AutoMl backend. Upon receiving ExportModel request from user, AutoMl backend can serve the pre-generated models to user if exists (by copying the files from internal path to user provided location), otherwise, AutoMl backend will call xPS ExportModel API to generate the model on the fly with the requesting format.", "id": "XPSVideoExportModelSpec", "properties": {"exportModelOutputConfig": {"description": "Contains the model format and internal location of the model files to be exported/downloaded. Use the Google Cloud Storage bucket name which is provided via TrainRequest.gcs_bucket_name to store the model files.", "items": {"$ref": "XPSExportModelOutputConfig"}, "type": "array"}}, "type": "object"}, "XPSVideoModelArtifactSpec": {"id": "XPSVideoModelArtifactSpec", "properties": {"exportArtifact": {"description": "The model binary files in different formats for model export.", "items": {"$ref": "XPSModelArtifactItem"}, "type": "array"}, "servingArtifact": {"$ref": "XPSModelArtifactItem", "description": "The default model binary file used for serving (e.g. batch predict) via public Cloud AI Platform API."}}, "type": "object"}, "XPSVideoObjectTrackingEvaluationMetrics": {"description": "Model evaluation metrics for ObjectTracking problems. Next tag: 10.", "id": "XPSVideoObjectTrackingEvaluationMetrics", "properties": {"boundingBoxMeanAveragePrecision": {"description": "Output only. The single metric for bounding boxes evaluation: the mean_average_precision averaged over all bounding_box_metrics_entries.", "format": "float", "type": "number"}, "boundingBoxMetricsEntries": {"description": "Output only. The bounding boxes match metrics for each Intersection-over-union threshold 0.05,0.10,...,0.95,0.96,0.97,0.98,0.99.", "items": {"$ref": "XPSBoundingBoxMetricsEntry"}, "type": "array"}, "evaluatedBoundingboxCount": {"description": "The number of bounding boxes used for model evaluation.", "format": "int32", "type": "integer"}, "evaluatedFrameCount": {"description": "The number of video frames used for model evaluation.", "format": "int32", "type": "integer"}, "evaluatedTrackCount": {"description": "The number of tracks used for model evaluation.", "format": "int32", "type": "integer"}, "trackMeanAveragePrecision": {"description": "Output only. The single metric for tracks accuracy evaluation: the mean_average_precision averaged over all track_metrics_entries.", "format": "float", "type": "number"}, "trackMeanBoundingBoxIou": {"description": "Output only. The single metric for tracks bounding box iou evaluation: the mean_bounding_box_iou averaged over all track_metrics_entries.", "format": "float", "type": "number"}, "trackMeanMismatchRate": {"description": "Output only. The single metric for tracking consistency evaluation: the mean_mismatch_rate averaged over all track_metrics_entries.", "format": "float", "type": "number"}, "trackMetricsEntries": {"description": "Output only. The tracks match metrics for each Intersection-over-union threshold 0.05,0.10,...,0.95,0.96,0.97,0.98,0.99.", "items": {"$ref": "XPSTrackMetricsEntry"}, "type": "array"}}, "type": "object"}, "XPSVideoObjectTrackingTrainResponse": {"id": "XPSVideoObjectTrackingTrainResponse", "properties": {"exportModelSpec": {"$ref": "XPSVideoExportModelSpec", "description": "Populated for AutoML request only."}, "modelArtifactSpec": {"$ref": "XPSVideoModelArtifactSpec", "description": "## The fields below are only populated under uCAIP request scope."}, "trainCostNodeSeconds": {"description": "The actual train cost of creating this model, expressed in node seconds, i.e. 3,600 value in this field means 1 node hour.", "format": "int64", "type": "string"}}, "type": "object"}, "XPSVideoTrainingOperationMetadata": {"id": "XPSVideoTrainingOperationMetadata", "properties": {"trainCostMilliNodeHour": {"description": "This is an estimation of the node hours necessary for training a model, expressed in milli node hours (i.e. 1,000 value in this field means 1 node hour). A node hour represents the time a virtual machine spends running your training job. The cost of one node running for one hour is a node hour.", "format": "int64", "type": "string"}}, "type": "object"}, "XPSVisionErrorAnalysisConfig": {"description": "The vision model error analysis configuration. Next tag: 3", "id": "XPSVisionErrorAnalysisConfig", "properties": {"exampleCount": {"description": "The number of query examples in error analysis.", "format": "int32", "type": "integer"}, "queryType": {"description": "The query type used in retrieval. The enum values are frozen in the foreseeable future.", "enum": ["QUERY_TYPE_UNSPECIFIED", "QUERY_TYPE_ALL_SIMILAR", "QUERY_TYPE_SAME_CLASS_SIMILAR", "QUERY_TYPE_SAME_CLASS_DISSIMILAR"], "enumDescriptions": ["Unspecified query type for model error analysis.", "Query similar samples across all classes in the dataset.", "Query similar samples from the same class of the input sample.", "Query dissimilar samples from the same class of the input sample."], "type": "string"}}, "type": "object"}, "XPSVisionTrainingOperationMetadata": {"deprecated": true, "id": "XPSVisionTrainingOperationMetadata", "properties": {"explanationUsage": {"$ref": "InfraUsage", "description": "Aggregated infra usage within certain time period, for billing report purpose if XAI is enable in training request."}}, "type": "object"}, "XPSVisualization": {"deprecated": true, "description": "Visualization configurations for image explanation.", "id": "XPSVisualization", "properties": {"clipPercentLowerbound": {"description": "Excludes attributions below the specified percentile, from the highlighted areas. Defaults to 62.", "format": "float", "type": "number"}, "clipPercentUpperbound": {"description": "Excludes attributions above the specified percentile from the highlighted areas. Using the clip_percent_upperbound and clip_percent_lowerbound together can be useful for filtering out noise and making it easier to see areas of strong attribution. Defaults to 99.9.", "format": "float", "type": "number"}, "colorMap": {"description": "The color scheme used for the highlighted areas. Defaults to PINK_GREEN for Integrated Gradients attribution, which shows positive attributions in green and negative in pink. Defaults to VIRIDIS for XRAI attribution, which highlights the most influential regions in yellow and the least influential in blue.", "enum": ["COLOR_MAP_UNSPECIFIED", "PINK_GREEN", "VIRIDIS", "RED", "GREEN", "RED_GREEN", "PINK_WHITE_GREEN"], "enumDescriptions": ["Should not be used.", "Positive: green. Negative: pink.", "Viridis color map: A perceptually uniform color mapping which is easier to see by those with colorblindness and progresses from yellow to green to blue. Positive: yellow. Negative: blue.", "Positive: red. Negative: red.", "Positive: green. Negative: green.", "Positive: green. Negative: red.", "PiYG palette."], "type": "string"}, "overlayType": {"description": "How the original image is displayed in the visualization. Adjusting the overlay can help increase visual clarity if the original image makes it difficult to view the visualization. Defaults to NONE.", "enum": ["OVERLAY_TYPE_UNSPECIFIED", "NONE", "ORIGINAL", "GRAYSCALE", "MASK_BLACK"], "enumDescriptions": ["Default value. This is the same as NONE.", "No overlay.", "The attributions are shown on top of the original image.", "The attributions are shown on top of grayscaled version of the original image.", "The attributions are used as a mask to reveal predictive parts of the image and hide the un-predictive parts."], "type": "string"}, "polarity": {"description": "Whether to only highlight pixels with positive contributions, negative or both. Defaults to POSITIVE.", "enum": ["POLARITY_UNSPECIFIED", "POSITIVE", "NEGATIVE", "BOTH"], "enumDescriptions": ["Default value. This is the same as POSITIVE.", "Highlights the pixels/outlines that were most influential to the model's prediction.", "Setting polarity to negative highlights areas that does not lead to the models's current prediction.", "Shows both positive and negative attributions."], "type": "string"}, "type": {"description": "Type of the image visualization. Only applicable to Integrated Gradients attribution. OUTLINES shows regions of attribution, while PIXELS shows per-pixel attribution. Defaults to OUTLINES.", "enum": ["TYPE_UNSPECIFIED", "PIXELS", "OUTLINES"], "enumDescriptions": ["Should not be used.", "Shows which pixel contributed to the image prediction.", "Shows which region contributed to the image prediction by outlining the region."], "type": "string"}}, "type": "object"}, "XPSXpsOperationMetadata": {"id": "XPSXpsOperationMetadata", "properties": {"exampleCount": {"description": "Optional. XPS server can opt to provide example count of the long running operation (e.g. training, data importing, batch prediction).", "format": "int64", "type": "string"}, "reportingMetrics": {"$ref": "XPSReportingMetrics", "description": "Metrics for the operation. By the time the operation is terminated (whether succeeded or failed) as returned from XPS, AutoML BE assumes the metrics are finalized. AutoML BE transparently posts the metrics to Chemist if it's not empty, regardless of the response content or error type. If user is supposed to be charged in case of cancellation/error, this field should be set. In the case where the type of LRO doesn't require any billing, this field should be left unset."}, "tablesTrainingOperationMetadata": {"$ref": "XPSTablesTrainingOperationMetadata"}, "videoBatchPredictOperationMetadata": {"$ref": "XPSVideoBatchPredictOperationMetadata"}, "videoTrainingOperationMetadata": {"$ref": "XPSVideoTrainingOperationMetadata"}, "visionTrainingOperationMetadata": {"$ref": "XPSVisionTrainingOperationMetadata"}}, "type": "object"}, "XPSXraiAttribution": {"deprecated": true, "description": "An explanation method that redistributes Integrated Gradients attributions to segmented regions, taking advantage of the model's fully differentiable structure. Refer to this paper for more details: https://arxiv.org/abs/1906.02825 Only supports image Models (modality is IMAGE).", "id": "XPSXraiAttribution", "properties": {"stepCount": {"description": "The number of steps for approximating the path integral. A good value to start is 50 and gradually increase until the sum to diff property is met within the desired error range. Valid range of its value is [1, 100], inclusively.", "format": "int32", "type": "integer"}}, "type": "object"}}, "servicePath": "", "title": "Cloud Natural Language API", "version": "v1", "version_module": true}