import pandas as pd
from deepdiff import DeepDiff
from dotenv import load_dotenv
import os
import smtplib
from email.mime.text import MIMEText
import google.generativeai as genai

# Load variables from .env
load_dotenv()
GEMINI_API_KEY = os.getenv("GEMINI_API_KEY")
EMAIL_USER = os.getenv("EMAIL_USER")       # your sender email
EMAIL_PASS = os.getenv("EMAIL_PASS")       # your sender email password or app password

if not GEMINI_API_KEY:
    raise ValueError("GEMINI_API_KEY is missing! Check your .env file")

if not EMAIL_USER or not EMAIL_PASS:
    raise ValueError("Email credentials missing in .env")

# Configure Gemini
genai.configure(api_key=GEMINI_API_KEY)

# ---------- Step 1 – Load BOM Files ----------
def load_bom(file_path):
    return pd.read_excel(file_path).to_dict(orient="records")

# ---------- Step 2 – Detect Changes ----------
def detect_bom_changes(old_bom, new_bom):
    return DeepDiff(old_bom, new_bom, ignore_order=True).to_dict()

# ---------- Step 3 – Summarize Changes ----------
def generate_summary(changes, focus_area):
    prompts = {"Procurement": f"""
        You are preparing a summary for the Procurement team. 
        Focus only on changes in supplier details, vendor references, cost-related fields, or part availability.
        Do not include assembly or compliance details. 

        Here are the BOM changes:
        {changes}

        Provide a clear, concise summary highlighting supplier updates, discontinued parts, and any potential sourcing risks.
        """,
        
        "Production": f"""
        You are preparing a summary for the Production/Manufacturing team. 
        Focus only on changes that impact assembly, materials, part numbers, or quantities.
        Ignore supplier and compliance details. 

        Here are the BOM changes:
        {changes}

        Write a summary that clearly explains what parts were added, removed, or replaced and how this impacts assembly or production workflow.
        """,
        
        "QA": f"""
        You are preparing a summary for the QA (Quality Assurance) team.
        Focus only on compliance-related changes, such as standards, material specifications, certifications, or quality notes. 
        Ignore supplier or assembly details.

        Here are the BOM changes:
        {changes}

        Write a summary that clearly highlights compliance risks, missing certifications, or changes that QA should verify.
        """
    }
    model = genai.GenerativeModel('gemini-1.5-flash')
    response = model.generate_content(prompts[focus_area])
    return response.text

# ---------- Step 4 – Send Email ----------
def send_email(to_email, subject, body):
    msg = MIMEText(body, "plain")
    msg["Subject"] = subject
    msg["From"] = EMAIL_USER
    msg["To"] = to_email

    try:
        with smtplib.SMTP_SSL("smtp.gmail.com", 465) as server:   # Gmail example
            server.login(EMAIL_USER, EMAIL_PASS)
            server.sendmail(EMAIL_USER, [to_email], msg.as_string())
        print(f"Email sent to {to_email}")
    except Exception as e:
        print(f"Failed to send email to {to_email}: {e}")

# ---------- Main ----------
if __name__ == "__main__":
    old_bom_file = r"D:\BOM\bom old version.xlsx"
    new_bom_file = r"D:\BOM\bom new version.xlsx"

    old_bom = load_bom(old_bom_file)
    new_bom = load_bom(new_bom_file)

    changes = detect_bom_changes(old_bom, new_bom)

    # Department emails (replace with actual ones)
    department_emails = {
        "Procurement": "<EMAIL>",
        "Production": "<EMAIL>",
        "QA": "<EMAIL>"
    }

    for dept, email in department_emails.items():
        summary = generate_summary(changes, dept)
        send_email(email, f"BOM Changes - {dept}", summary)
