{"auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/content": {"description": "Manage your product listings and accounts for Google Shopping"}}}}, "basePath": "", "baseUrl": "https://merchantapi.googleapis.com/", "batchPath": "batch", "canonicalName": "Merchant", "description": "Programmatically manage your Merchant Center Accounts.", "discoveryVersion": "v1", "documentationLink": "https://developers.google.com/merchant/api", "fullyEncodeReservedExpansion": true, "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "merchantapi:issueresolution_v1beta", "kind": "discovery#restDescription", "mtlsRootUrl": "https://merchantapi.mtls.googleapis.com/", "name": "merchantapi", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"accounts": {"resources": {"aggregateProductStatuses": {"methods": {"list": {"description": "Lists the `AggregateProductStatuses` resources for your merchant account. The response might contain fewer items than specified by `pageSize`. If `pageToken` was returned in previous request, it can be used to obtain additional results.", "flatPath": "issueresolution/v1beta/accounts/{accountsId}/aggregateProductStatuses", "httpMethod": "GET", "id": "merchantapi.accounts.aggregateProductStatuses.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. A filter expression that filters the aggregate product statuses. Filtering is only supported by the `reporting_context` and `country` field. For example: `reporting_context = \"SHOPPING_ADS\" AND country = \"US\"`.", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. The maximum number of aggregate product statuses to return. The service may return fewer than this value. If unspecified, at most 25 aggregate product statuses are returned. The maximum value is 250; values above 250 are coerced to 250.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A page token, received from a previous `ListAggregateProductStatuses` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListAggregateProductStatuses` must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The account to list aggregate product statuses for. Format: `accounts/{account}`", "location": "path", "pattern": "^accounts/[^/]+$", "required": true, "type": "string"}}, "path": "issueresolution/v1beta/{+parent}/aggregateProductStatuses", "response": {"$ref": "ListAggregateProductStatusesResponse"}, "scopes": ["https://www.googleapis.com/auth/content"]}}}}}, "issueresolution": {"methods": {"renderaccountissues": {"description": "Provide a list of business's account issues with an issue resolution content and available actions. This content and actions are meant to be rendered and shown in third-party applications.", "flatPath": "issueresolution/v1beta/accounts/{accountsId}:renderaccountissues", "httpMethod": "POST", "id": "merchantapi.issueresolution.renderaccountissues", "parameterOrder": ["name"], "parameters": {"languageCode": {"description": "Optional. The [IETF BCP-47](https://tools.ietf.org/html/bcp47) language code used to localize issue resolution content. If not set, the result will be in default language `en-US`.", "location": "query", "type": "string"}, "name": {"description": "Required. The account to fetch issues for. Format: `accounts/{account}`", "location": "path", "pattern": "^accounts/[^/]+$", "required": true, "type": "string"}, "timeZone": {"description": "Optional. The [IANA](https://www.iana.org/time-zones) timezone used to localize times in an issue resolution content. For example 'America/Los_Angeles'. If not set, results will use as a default UTC.", "location": "query", "type": "string"}}, "path": "issueresolution/v1beta/{+name}:renderaccountissues", "request": {"$ref": "RenderIssuesRequestPayload"}, "response": {"$ref": "RenderAccountIssuesResponse"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "renderproductissues": {"description": "Provide a list of issues for business's product with an issue resolution content and available actions. This content and actions are meant to be rendered and shown in third-party applications.", "flatPath": "issueresolution/v1beta/accounts/{accountsId}/products/{productsId}:renderproductissues", "httpMethod": "POST", "id": "merchantapi.issueresolution.renderproductissues", "parameterOrder": ["name"], "parameters": {"languageCode": {"description": "Optional. The [IETF BCP-47](https://tools.ietf.org/html/bcp47) language code used to localize an issue resolution content. If not set, the result will be in default language `en-US`.", "location": "query", "type": "string"}, "name": {"description": "Required. The name of the product to fetch issues for. Format: `accounts/{account}/products/{product}`", "location": "path", "pattern": "^accounts/[^/]+/products/[^/]+$", "required": true, "type": "string"}, "timeZone": {"description": "Optional. The [IANA](https://www.iana.org/time-zones) timezone used to localize times in an issue resolution content. For example 'America/Los_Angeles'. If not set, results will use as a default UTC.", "location": "query", "type": "string"}}, "path": "issueresolution/v1beta/{+name}:renderproductissues", "request": {"$ref": "RenderIssuesRequestPayload"}, "response": {"$ref": "RenderProductIssuesResponse"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "triggeraction": {"description": "Start an action. The action can be requested by a business in third-party application. Before the business can request the action, the third-party application needs to show them action specific content and display a user input form. The action can be successfully started only once all `required` inputs are provided. If any `required` input is missing, or invalid value was provided, the service will return 400 error. Validation errors will contain Ids for all problematic field together with translated, human readable error messages that can be shown to the user.", "flatPath": "issueresolution/v1beta/accounts/{accountsId}:triggeraction", "httpMethod": "POST", "id": "merchantapi.issueresolution.triggeraction", "parameterOrder": ["name"], "parameters": {"languageCode": {"description": "Optional. Language code [IETF BCP 47 syntax](https://tools.ietf.org/html/bcp47) used to localize the response. If not set, the result will be in default language `en-US`.", "location": "query", "type": "string"}, "name": {"description": "Required. The business's account that is triggering the action. Format: `accounts/{account}`", "location": "path", "pattern": "^accounts/[^/]+$", "required": true, "type": "string"}}, "path": "issueresolution/v1beta/{+name}:triggeraction", "request": {"$ref": "TriggerActionPayload"}, "response": {"$ref": "TriggerActionResponse"}, "scopes": ["https://www.googleapis.com/auth/content"]}}}}, "revision": "********", "rootUrl": "https://merchantapi.googleapis.com/", "schemas": {"Action": {"description": "An actionable step that can be executed to solve the issue.", "id": "Action", "properties": {"builtinSimpleAction": {"$ref": "BuiltInSimpleAction", "description": "Action implemented and performed in (your) third-party application. The application should point the business to the place, where they can access the corresponding functionality or provide instructions, if the specific functionality is not available."}, "builtinUserInputAction": {"$ref": "BuiltInUserInputAction", "description": "Action implemented and performed in (your) third-party application. The application needs to show an additional content and input form to the business as specified for given action. They can trigger the action only when they provided all required inputs."}, "buttonLabel": {"description": "Label of the action button.", "type": "string"}, "externalAction": {"$ref": "ExternalAction", "description": "Action that is implemented and performed outside of (your) third-party application. The application needs to redirect the business to the external location where they can perform the action."}, "isAvailable": {"description": "Controlling whether the button is active or disabled. The value is 'false' when the action was already requested or is not available. If the action is not available then a reason will be present. If (your) third-party application shows a disabled button for action that is not available, then it should also show reasons.", "type": "boolean"}, "reasons": {"description": "List of reasons why the action is not available. The list of reasons is empty if the action is available. If there is only one reason, it can be displayed next to the disabled button. If there are more reasons, all of them should be displayed, for example in a pop-up dialog.", "items": {"$ref": "Reason"}, "type": "array"}}, "type": "object"}, "ActionFlow": {"description": "Flow that can be selected for an action. When a business selects a flow, application should open a dialog with more information and input form.", "id": "ActionFlow", "properties": {"dialogButtonLabel": {"description": "Label for the button to trigger the action from the action dialog. For example: \"Request review\"", "type": "string"}, "dialogCallout": {"$ref": "Callout", "description": "Important message to be highlighted in the request dialog. For example: \"You can only request a review for disagreeing with this issue once. If it's not approved, you'll need to fix the issue and wait a few days before you can request another review.\""}, "dialogMessage": {"$ref": "TextWithTooltip", "description": "Message displayed in the request dialog. For example: \"Make sure you've fixed all your country-specific issues. If not, you may have to wait 7 days to request another review\". There may be an more information to be shown in a tooltip."}, "dialogTitle": {"description": "Title of the request dialog. For example: \"Before you request a review\"", "type": "string"}, "id": {"description": "Not for display but need to be sent back for the selected action flow.", "type": "string"}, "inputs": {"description": "A list of input fields.", "items": {"$ref": "InputField"}, "type": "array"}, "label": {"description": "Text value describing the intent for the action flow. It can be used as an input label if business needs to pick one of multiple flows. For example: \"I disagree with the issue\"", "type": "string"}}, "type": "object"}, "ActionInput": {"description": "Input provided by the business.", "id": "ActionInput", "properties": {"actionFlowId": {"description": "Required. Id of the selected action flow.", "type": "string"}, "inputValues": {"description": "Required. Values for input fields.", "items": {"$ref": "InputValue"}, "type": "array"}}, "type": "object"}, "AdditionalContent": {"description": "Long text from external source.", "id": "Additional<PERSON>ontent", "properties": {"paragraphs": {"description": "Long text organized into paragraphs.", "items": {"type": "string"}, "type": "array"}, "title": {"description": "Title of the additional content;", "type": "string"}}, "type": "object"}, "AggregateProductStatus": {"description": "Aggregate product statuses for a given reporting context and country.", "id": "AggregateProductStatus", "properties": {"country": {"description": "The country of the aggregate product statuses. Represented as a [CLDR territory code](https://github.com/unicode-org/cldr/blob/latest/common/main/en.xml).", "type": "string"}, "itemLevelIssues": {"description": "The product issues that affect the given reporting context and country.", "items": {"$ref": "ItemLevelIssue"}, "type": "array"}, "name": {"description": "Identifier. The name of the `AggregateProductStatuses` resource. Format: `accounts/{account}/aggregateProductStatuses/{aggregateProductStatuses}`", "type": "string"}, "reportingContext": {"description": "The reporting context of the aggregate product statuses.", "enum": ["REPORTING_CONTEXT_ENUM_UNSPECIFIED", "SHOPPING_ADS", "DISCOVERY_ADS", "DEMAND_GEN_ADS", "DEMAND_GEN_ADS_DISCOVER_SURFACE", "VIDEO_ADS", "DISPLAY_ADS", "LOCAL_INVENTORY_ADS", "VEHICLE_INVENTORY_ADS", "FREE_LISTINGS", "FREE_LOCAL_LISTINGS", "FREE_LOCAL_VEHICLE_LISTINGS", "YOUTUBE_AFFILIATE", "YOUTUBE_SHOPPING", "CLOUD_RETAIL", "LOCAL_CLOUD_RETAIL", "PRODUCT_REVIEWS", "MERCHANT_REVIEWS", "YOUTUBE_CHECKOUT"], "enumDeprecated": [false, false, true, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false], "enumDescriptions": ["Not specified.", "[Shopping ads](https://support.google.com/merchants/answer/6149970).", "Deprecated: Use `DEMAND_GEN_ADS` instead. [Discovery and Demand Gen ads](https://support.google.com/merchants/answer/********).", "[Demand Gen ads](https://support.google.com/merchants/answer/********).", "[Demand Gen ads on Discover surface](https://support.google.com/merchants/answer/********).", "[Video ads](https://support.google.com/google-ads/answer/6340491).", "[Display ads](https://support.google.com/merchants/answer/6069387).", "[Local inventory ads](https://support.google.com/merchants/answer/3271956).", "[Vehicle inventory ads](https://support.google.com/merchants/answer/11544533).", "[Free product listings](https://support.google.com/merchants/answer/9199328).", "[Free local product listings](https://support.google.com/merchants/answer/9825611).", "[Free local vehicle listings](https://support.google.com/merchants/answer/11544533).", "[Youtube Affiliate](https://support.google.com/youtube/answer/13376398).", "[YouTube Shopping](https://support.google.com/merchants/answer/13478370).", "[Cloud retail](https://cloud.google.com/solutions/retail).", "[Local cloud retail](https://cloud.google.com/solutions/retail).", "[Product Reviews](https://support.google.com/merchants/answer/********).", "[Merchant Reviews](https://developers.google.com/merchant-review-feeds).", "YouTube Checkout ."], "type": "string"}, "stats": {"$ref": "Stats", "description": "Products statistics for the given reporting context and country."}}, "type": "object"}, "Breakdown": {"description": "A detailed impact breakdown for a group of regions where the impact of the issue on different shopping destinations is the same.", "id": "Breakdown", "properties": {"details": {"description": "Human readable, localized description of issue's effect on different targets. Should be rendered as a list. For example: * \"Products not showing in ads\" * \"Products not showing organically\"", "items": {"type": "string"}, "type": "array"}, "regions": {"description": "Lists of regions. Should be rendered as a title for this group of details. The full list should be shown to the business. If the list is too long, it is recommended to make it expandable.", "items": {"$ref": "Region"}, "type": "array"}}, "type": "object"}, "BuiltInSimpleAction": {"description": "Action that is implemented and performed in (your) third-party application. Represents various functionality that is expected to be available to business and will help them with resolving the issue. The application should point the business to the place, where they can access the corresponding functionality. If the functionality is not supported, it is recommended to explain the situation to the business and provide them with instructions how to solve the issue.", "id": "BuiltInSimpleAction", "properties": {"additionalContent": {"$ref": "Additional<PERSON>ontent", "description": "Long text from an external source that should be available to the business. Present when the type is `SHOW_ADDITIONAL_CONTENT`."}, "attributeCode": {"description": "The attribute that needs to be updated. Present when the type is `EDIT_ITEM_ATTRIBUTE`. This field contains a code for attribute, represented in snake_case. You can find a list of product's attributes, with their codes [here](https://support.google.com/merchants/answer/7052112).", "type": "string"}, "type": {"description": "The type of action that represents a functionality that is expected to be available in third-party application.", "enum": ["BUILT_IN_SIMPLE_ACTION_TYPE_UNSPECIFIED", "VERIFY_PHONE", "CLAIM_WEBSITE", "ADD_PRODUCTS", "ADD_CONTACT_INFO", "LINK_ADS_ACCOUNT", "ADD_BUSINESS_REGISTRATION_NUMBER", "EDIT_ITEM_ATTRIBUTE", "FIX_ACCOUNT_ISSUE", "SHOW_ADDITIONAL_CONTENT"], "enumDescriptions": ["Default value. Will never be provided by the API.", "Redirect the business to the part of your application where they can verify their phone.", "Redirect the business to the part of your application where they can claim their website.", "Redirect the business to the part of your application where they can add products.", "Open a form where the business can edit their contact information.", "Redirect the business to the part of your application where they can link ads account.", "Open a form where the business can add their business registration number.", "Open a form where the business can edit an attribute. The attribute that needs to be updated is specified in attribute_code field of the action.", "Redirect the business from the product issues to the diagnostic page with their account issues in your application. This action will be returned only for product issues that are caused by an account issue and thus the business should resolve the problem on the account level.", "Show additional content to the business. This action will be used for example to deliver a justification from national authority."], "type": "string"}}, "type": "object"}, "BuiltInUserInputAction": {"description": "Action that is implemented and performed in (your) third-party application. The application needs to show an additional content and input form to the business. They can start the action only when they provided all required inputs. The application will request processing of the action by calling the [triggeraction method](https://developers.google.com/merchant/api/reference/rest/issueresolution_v1beta/issueresolution/triggeraction).", "id": "BuiltInUserInputAction", "properties": {"actionContext": {"description": "Contains the action's context that must be included as part of the TriggerActionPayload.action_context in TriggerActionRequest.payload to call the `triggeraction` method. The content should be treated as opaque and must not be modified.", "type": "string"}, "flows": {"description": "Actions may provide multiple different flows. Business selects one that fits best to their intent. Selecting the flow is the first step in user's interaction with the action. It affects what input fields will be available and required and also how the request will be processed.", "items": {"$ref": "ActionFlow"}, "type": "array"}}, "type": "object"}, "Callout": {"description": "An important message that should be highlighted. Usually displayed as a banner.", "id": "Callout", "properties": {"fullMessage": {"$ref": "TextWithTooltip", "description": "A full message that needs to be shown to the business."}, "styleHint": {"description": "Can be used to render messages with different severity in different styles. Snippets off all types contain important information that should be displayed to the business.", "enum": ["CALLOUT_STYLE_HINT_UNSPECIFIED", "ERROR", "WARNING", "INFO"], "enumDescriptions": ["Default value. Will never be provided by the API.", "The most important type of information highlighting problems, like an unsuccessful outcome of previously requested actions.", "Information warning about pending problems, risks or deadlines.", "Default severity for important information like pending status of previously requested action or cooldown for re-review."], "type": "string"}}, "type": "object"}, "CheckboxInput": {"description": "Checkbox input allows the business to provide a boolean value. Corresponds to the [html input type=checkbox](https://www.w3.org/TR/2012/WD-html-markup-********/input.checkbox.html#input.checkbox). If the business checks the box, the input value for the field is `true`, otherwise it is `false`. This type of input is often used as a confirmation that the business completed required steps before they are allowed to start the action. In such a case, the input field is marked as required and the button to trigger the action should stay disabled until the business checks the box.", "id": "CheckboxInput", "properties": {}, "type": "object"}, "CheckboxInputValue": {"description": "Value for checkbox input field.", "id": "CheckboxInputValue", "properties": {"value": {"description": "Required. True if the business checked the box field. False otherwise.", "type": "boolean"}}, "type": "object"}, "ChoiceInput": {"description": "Choice input allows the business to select one of the offered choices. Some choices may be linked to additional input fields that should be displayed under or next to the choice option. The value for the additional input field needs to be provided only when the specific choice is selected by the the business. For example, additional input field can be hidden or disabled until the business selects the specific choice.", "id": "ChoiceInput", "properties": {"options": {"description": "A list of choices. Only one option can be selected.", "items": {"$ref": "ChoiceInputOption"}, "type": "array"}}, "type": "object"}, "ChoiceInputOption": {"description": "A choice that the business can select.", "id": "ChoiceInputOption", "properties": {"additionalInput": {"$ref": "InputField", "description": "Input that should be displayed when this option is selected. The additional input will not contain a `ChoiceInput`."}, "id": {"description": "Not for display but need to be sent back for the selected choice option.", "type": "string"}, "label": {"$ref": "TextWithTooltip", "description": "Short description of the choice option. There may be more information to be shown as a tooltip."}}, "type": "object"}, "ChoiceInputValue": {"description": "Value for choice input field.", "id": "ChoiceInputValue", "properties": {"choiceInputOptionId": {"description": "Required. Id of the option that was selected by the business.", "type": "string"}}, "type": "object"}, "ExternalAction": {"description": "Action that is implemented and performed outside of the third-party application. It should redirect the business to the provided URL of an external system where they can perform the action. For example to request a review in the Merchant Center.", "id": "ExternalAction", "properties": {"type": {"description": "The type of external action.", "enum": ["EXTERNAL_ACTION_TYPE_UNSPECIFIED", "REVIEW_PRODUCT_ISSUE_IN_MERCHANT_CENTER", "REVIEW_ACCOUNT_ISSUE_IN_MERCHANT_CENTER", "LEGAL_APPEAL_IN_HELP_CENTER", "VERIFY_IDENTITY_IN_MERCHANT_CENTER"], "enumDescriptions": ["Default value. Will never be provided by the API.", "Redirect to Merchant Center where the business can request a review for issue related to their product.", "Redirect to Merchant Center where the business can request a review for issue related to their account.", "Redirect to the form in Help Center where the business can request a legal appeal for the issue.", "Redirect to Merchant Center where the business can perform identity verification."], "type": "string"}, "uri": {"description": "URL to external system, for example Merchant Center, where the business can perform the action.", "type": "string"}}, "type": "object"}, "Impact": {"description": "Overall impact of the issue.", "id": "Impact", "properties": {"breakdowns": {"description": "Detailed impact breakdown. Explains the types of restriction the issue has in different shopping destinations and territory. If present, it should be rendered to the business. Can be shown as a mouse over dropdown or a dialog. Each breakdown item represents a group of regions with the same impact details.", "items": {"$ref": "Breakdown"}, "type": "array"}, "message": {"description": "Optional. Message summarizing the overall impact of the issue. If present, it should be rendered to the business. For example: \"Disapproves 90k offers in 25 countries\"", "type": "string"}, "severity": {"description": "The severity of the issue.", "enum": ["SEVERITY_UNSPECIFIED", "ERROR", "WARNING", "INFO"], "enumDescriptions": ["Default value. Will never be provided by the API.", "Causes either an account suspension or an item disapproval. Errors should be resolved as soon as possible to ensure items are eligible to appear in results again.", "Warnings can negatively impact the performance of ads and can lead to item or account suspensions in the future unless the issue is resolved.", "Infos are suggested optimizations to increase data quality. Resolving these issues is recommended, but not required."], "type": "string"}}, "type": "object"}, "InputField": {"description": "Input field that needs to be available to the business. If the field is marked as required, then a value needs to be provided for a successful processing of the request.", "id": "InputField", "properties": {"checkboxInput": {"$ref": "CheckboxInput", "description": "Input field to provide a boolean value. Corresponds to the [html input type=checkbox](https://www.w3.org/TR/2012/WD-html-markup-********/input.checkbox.html#input.checkbox)."}, "choiceInput": {"$ref": "ChoiceInput", "description": "Input field to select one of the offered choices. Corresponds to the [html input type=radio](https://www.w3.org/TR/2012/WD-html-markup-********/input.radio.html#input.radio)."}, "id": {"description": "Not for display but need to be sent back for the given input field.", "type": "string"}, "label": {"$ref": "TextWithTooltip", "description": "Input field label. There may be more information to be shown in a tooltip."}, "required": {"description": "Whether the field is required. The action button needs to stay disabled till values for all required fields are provided.", "type": "boolean"}, "textInput": {"$ref": "TextInput", "description": "Input field to provide text information. Corresponds to the [html input type=text](https://www.w3.org/TR/2012/WD-html-markup-********/input.text.html#input.text) or [html textarea](https://www.w3.org/TR/2012/WD-html-markup-********/textarea.html#textarea)."}}, "type": "object"}, "InputValue": {"description": "Input provided by the business for input field.", "id": "InputValue", "properties": {"checkboxInputValue": {"$ref": "CheckboxInputValue", "description": "Value for checkbox input field."}, "choiceInputValue": {"$ref": "ChoiceInputValue", "description": "Value for choice input field."}, "inputFieldId": {"description": "Required. Id of the corresponding input field.", "type": "string"}, "textInputValue": {"$ref": "TextInputValue", "description": "Value for text input field."}}, "type": "object"}, "ItemLevelIssue": {"description": "The ItemLevelIssue of the product status.", "id": "ItemLevelIssue", "properties": {"attribute": {"description": "The attribute's name, if the issue is caused by a single attribute.", "type": "string"}, "code": {"description": "The error code of the issue.", "type": "string"}, "description": {"description": "A short issue description in English.", "type": "string"}, "detail": {"description": "A detailed issue description in English.", "type": "string"}, "documentationUri": {"description": "The URL of a web page to help with resolving this issue.", "type": "string"}, "productCount": {"description": "The number of products affected by this issue.", "format": "int64", "type": "string"}, "resolution": {"description": "Whether the issue can be resolved by the merchant.", "enum": ["RESOLUTION_UNSPECIFIED", "MERCHANT_ACTION", "PENDING_PROCESSING"], "enumDescriptions": ["Not specified.", "The issue can be resolved by the merchant.", "The issue will be resolved auomatically."], "type": "string"}, "severity": {"description": "How this issue affects serving of the offer.", "enum": ["SEVERITY_UNSPECIFIED", "NOT_IMPACTED", "DEMOTED", "DISAPPROVED"], "enumDescriptions": ["Not specified.", "This issue represents a warning and does not have a direct affect on the product.", "The product is demoted and most likely have limited performance in search results", "Issue disapproves the product."], "type": "string"}}, "type": "object"}, "ListAggregateProductStatusesResponse": {"description": "Response message for the `ListAggregateProductStatuses` method.", "id": "ListAggregateProductStatusesResponse", "properties": {"aggregateProductStatuses": {"description": "The `AggregateProductStatuses` resources for the given account.", "items": {"$ref": "AggregateProductStatus"}, "type": "array"}, "nextPageToken": {"description": "A token, which can be sent as `pageToken` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}}, "type": "object"}, "ProductChange": {"description": "The change that happened to the product including old value, new value, country code as the region code and reporting context.", "id": "ProductChange", "properties": {"newValue": {"description": "The new value of the changed resource or attribute. If empty, it means that the product was deleted. Will have one of these values : (`approved`, `pending`, `disapproved`, ``)", "type": "string"}, "oldValue": {"description": "The old value of the changed resource or attribute. If empty, it means that the product was created. Will have one of these values : (`approved`, `pending`, `disapproved`, ``)", "type": "string"}, "regionCode": {"description": "Countries that have the change (if applicable). Represented in the ISO 3166 format.", "type": "string"}, "reportingContext": {"description": "Reporting contexts that have the change (if applicable). Currently this field supports only (`SHOPPING_ADS`, `LOCAL_INVENTORY_ADS`, `YOUTUBE_SHOPPING`, `YOUTUBE_CHECKOUT`, `YOUTUBE_AFFILIATE`) from the enum value [ReportingContextEnum](/merchant/api/reference/rest/Shared.Types/ReportingContextEnum)", "enum": ["REPORTING_CONTEXT_ENUM_UNSPECIFIED", "SHOPPING_ADS", "DISCOVERY_ADS", "DEMAND_GEN_ADS", "DEMAND_GEN_ADS_DISCOVER_SURFACE", "VIDEO_ADS", "DISPLAY_ADS", "LOCAL_INVENTORY_ADS", "VEHICLE_INVENTORY_ADS", "FREE_LISTINGS", "FREE_LOCAL_LISTINGS", "FREE_LOCAL_VEHICLE_LISTINGS", "YOUTUBE_AFFILIATE", "YOUTUBE_SHOPPING", "CLOUD_RETAIL", "LOCAL_CLOUD_RETAIL", "PRODUCT_REVIEWS", "MERCHANT_REVIEWS", "YOUTUBE_CHECKOUT"], "enumDeprecated": [false, false, true, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false], "enumDescriptions": ["Not specified.", "[Shopping ads](https://support.google.com/merchants/answer/6149970).", "Deprecated: Use `DEMAND_GEN_ADS` instead. [Discovery and Demand Gen ads](https://support.google.com/merchants/answer/********).", "[Demand Gen ads](https://support.google.com/merchants/answer/********).", "[Demand Gen ads on Discover surface](https://support.google.com/merchants/answer/********).", "[Video ads](https://support.google.com/google-ads/answer/6340491).", "[Display ads](https://support.google.com/merchants/answer/6069387).", "[Local inventory ads](https://support.google.com/merchants/answer/3271956).", "[Vehicle inventory ads](https://support.google.com/merchants/answer/11544533).", "[Free product listings](https://support.google.com/merchants/answer/9199328).", "[Free local product listings](https://support.google.com/merchants/answer/9825611).", "[Free local vehicle listings](https://support.google.com/merchants/answer/11544533).", "[Youtube Affiliate](https://support.google.com/youtube/answer/13376398).", "[YouTube Shopping](https://support.google.com/merchants/answer/13478370).", "[Cloud retail](https://cloud.google.com/solutions/retail).", "[Local cloud retail](https://cloud.google.com/solutions/retail).", "[Product Reviews](https://support.google.com/merchants/answer/********).", "[Merchant Reviews](https://developers.google.com/merchant-review-feeds).", "YouTube Checkout ."], "type": "string"}}, "type": "object"}, "ProductStatusChangeMessage": {"description": "The message that the merchant will receive to notify about product status change event", "id": "ProductStatusChangeMessage", "properties": {"account": {"description": "The target account that owns the entity that changed. Format : `accounts/{merchant_id}`", "type": "string"}, "attribute": {"description": "The attribute in the resource that changed, in this case it will be always `Status`.", "enum": ["ATTRIBUTE_UNSPECIFIED", "STATUS"], "enumDescriptions": ["Unspecified attribute", "Status of the changed entity"], "type": "string"}, "changes": {"description": "A message to describe the change that happened to the product", "items": {"$ref": "ProductChange"}, "type": "array"}, "eventTime": {"description": "The time at which the event was generated. If you want to order the notification messages you receive you should rely on this field not on the order of receiving the notifications.", "format": "google-datetime", "type": "string"}, "expirationTime": {"description": "Optional. The product expiration time. This field will not be set if the notification is sent for a product deletion event.", "format": "google-datetime", "type": "string"}, "managingAccount": {"description": "The account that manages the merchant's account. can be the same as merchant id if it is standalone account. Format : `accounts/{service_provider_id}`", "type": "string"}, "resource": {"description": "The product name. Format: `accounts/{account}/products/{product}`", "type": "string"}, "resourceId": {"description": "The product id.", "type": "string"}, "resourceType": {"description": "The resource that changed, in this case it will always be `Product`.", "enum": ["RESOURCE_UNSPECIFIED", "PRODUCT"], "enumDescriptions": ["Unspecified resource", "Resource type : product"], "type": "string"}}, "type": "object"}, "Reason": {"description": "A single reason why the action is not available.", "id": "Reason", "properties": {"action": {"$ref": "Action", "description": "Optional. An action that needs to be performed to solve the problem represented by this reason. This action will always be available. Should be rendered as a link or button next to the summarizing message. For example, the review may be available only once the business configure all required attributes. In such a situation this action can be a link to the form, where they can fill the missing attribute to unblock the main action."}, "detail": {"description": "Detailed explanation of the reason. Should be displayed as a hint if present.", "type": "string"}, "message": {"description": "Messages summarizing the reason, why the action is not available. For example: \"Review requested on Jan 03. Review requests can take a few days to complete.\"", "type": "string"}}, "type": "object"}, "Region": {"description": "Region with code and localized name.", "id": "Region", "properties": {"code": {"description": "The [CLDR territory code] (http://www.unicode.org/repos/cldr/tags/latest/common/main/en.xml)", "type": "string"}, "name": {"description": "The localized name of the region. For region with code='001' the value is 'All countries' or the equivalent in other languages.", "type": "string"}}, "type": "object"}, "RenderAccountIssuesResponse": {"description": "Response containing an issue resolution content and actions for listed account issues.", "id": "RenderAccountIssuesResponse", "properties": {"renderedIssues": {"description": "List of account issues for a given account. This list can be shown with compressed, expandable items. In the compressed form, the title and impact should be shown for each issue. Once the issue is expanded, the detailed content and available actions should be rendered.", "items": {"$ref": "RenderedIssue"}, "type": "array"}}, "type": "object"}, "RenderIssuesRequestPayload": {"description": "The payload for configuring how the content should be rendered.", "id": "RenderIssuesRequestPayload", "properties": {"contentOption": {"description": "Optional. How the detailed content should be returned. Default option is to return the content as a pre-rendered HTML text.", "enum": ["CONTENT_OPTION_UNSPECIFIED", "PRE_RENDERED_HTML"], "enumDescriptions": ["Default value. Will never be provided by the API.", "Returns the detail of the issue as a pre-rendered HTML text."], "type": "string"}, "userInputActionOption": {"description": "Optional. How actions with user input form should be handled. If not provided, actions will be returned as links that points the business to Merchant Center where they can request the action.", "enum": ["USER_INPUT_ACTION_RENDERING_OPTION_UNSPECIFIED", "REDIRECT_TO_MERCHANT_CENTER", "BUILT_IN_USER_INPUT_ACTIONS"], "enumDescriptions": ["Default value. Will never be provided by the API.", "Actions that require user input are represented only as links that points the business to Merchant Center where they can request the action. Provides easier to implement alternative to `BUILT_IN_USER_INPUT_ACTIONS`.", "Returns content and input form definition for each complex action. Your application needs to display this content and input form to the business before they can request processing of the action. To start the action, your application needs to call the `triggeraction` method."], "type": "string"}}, "type": "object"}, "RenderProductIssuesResponse": {"description": "Response containing an issue resolution content and actions for listed product issues.", "id": "RenderProductIssuesResponse", "properties": {"renderedIssues": {"description": "List of issues for a given product. This list can be shown with compressed, expandable items. In the compressed form, the title and impact should be shown for each issue. Once the issue is expanded, the detailed content and available actions should be rendered.", "items": {"$ref": "RenderedIssue"}, "type": "array"}}, "type": "object"}, "RenderedIssue": {"description": "An issue affecting specific business or their product.", "id": "RenderedIssue", "properties": {"actions": {"description": "A list of actionable steps that can be executed to solve the issue. An example is requesting a re-review or providing arguments when business disagrees with the issue. Actions that are supported in (your) third-party application can be rendered as buttons and should be available to the business when they expand the issue.", "items": {"$ref": "Action"}, "type": "array"}, "impact": {"$ref": "Impact", "description": "Clarifies the severity of the issue. The summarizing message, if present, should be shown right under the title for each issue. It helps business to quickly understand the impact of the issue. The detailed breakdown helps the business to fully understand the impact of the issue. It can be rendered as dialog that opens when the business mouse over the summarized impact statement. Issues with different severity can be styled differently. They may use a different color or icon to signal the difference between `ERROR`, `WARNING` and `INFO`."}, "prerenderedContent": {"description": "Details of the issue as a pre-rendered HTML. HTML elements contain CSS classes that can be used to customize the style of the content. Always sanitize the HTML before embedding it directly to your application. The sanitizer needs to allow basic HTML tags, such as: `div`, `span`, `p`, `a`, `ul`, `li`, `table`, `tr`, `td`. For example, you can use [DOMPurify](https://www.npmjs.com/package/dompurify). CSS classes: * `issue-detail` - top level container for the detail of the issue * `callout-banners` - section of the `issue-detail` with callout banners * `callout-banner` - single callout banner, inside `callout-banners` * `callout-banner-info` - callout with important information (default) * `callout-banner-warning` - callout with a warning * `callout-banner-error` - callout informing about an error (most severe) * `issue-content` - section of the `issue-detail`, contains multiple `content-element` * `content-element` - content element such as a list, link or paragraph, inside `issue-content` * `root-causes` - unordered list with items describing root causes of the issue, inside `issue-content` * `root-causes-intro` - intro text before the `root-causes` list, inside `issue-content` * `segment` - section of the text, `span` inside paragraph * `segment-attribute` - section of the text that represents a product attribute, for example 'image\\_link' * `segment-literal` - section of the text that contains a special value, for example '0-1000 kg' * `segment-bold` - section of the text that should be rendered as bold * `segment-italic` - section of the text that should be rendered as italic * `tooltip` - used on paragraphs that should be rendered with a tooltip. A section of the text in such a paragraph will have a class `tooltip-text` and is intended to be shown in a mouse over dialog. If the style is not used, the `tooltip-text` section would be shown on a new line, after the main part of the text. * `tooltip-text` - marks a section of the text within a `tooltip`, that is intended to be shown in a mouse over dialog. * `tooltip-icon` - marks a section of the text within a `tooltip`, that can be replaced with a tooltip icon, for example '?' or 'i'. By default, this section contains a `br` tag, that is separating the main text and the tooltip text when the style is not used. * `tooltip-style-question` - the tooltip shows helpful information, can use the '?' as an icon. * `tooltip-style-info` - the tooltip adds additional information fitting to the context, can use the 'i' as an icon. * `content-moderation` - marks the paragraph that explains how the issue was identified. * `new-element` - Present for new elements added to the pre-rendered content in the future. To make sure that a new content element does not break your style, you can hide everything with this class.", "type": "string"}, "prerenderedOutOfCourtDisputeSettlement": {"description": "Pre-rendered HTML that contains a link to the external location where the ODS can be requested and instructions for how to request it. HTML elements contain CSS classes that can be used to customize the style of this snippet. Always sanitize the HTML before embedding it directly to your application. The sanitizer needs to allow basic HTML tags, such as: `div`, `span`, `p`, `a`, `ul`, `li`, `table`, `tr`, `td`. For example, you can use [DOMPurify](https://www.npmjs.com/package/dompurify). CSS classes: * `ods-section`* - wrapper around the out-of-court dispute resolution section * `ods-description`* - intro text for the out-of-court dispute resolution. It may contain multiple segments and a link. * `ods-param`* - wrapper around the header-value pair for parameters that the business may need to provide during the ODS process. * `ods-routing-id`* - ods param for the Routing ID. * `ods-reference-id`* - ods param for the Routing ID. * `ods-param-header`* - header for the ODS parameter * `ods-param-value`* - value of the ODS parameter. This value should be rendered in a way that it is easy for the user to identify and copy. * `segment` - section of the text, `span` inside paragraph * `segment-attribute` - section of the text that represents a product attribute, for example 'image\\_link' * `segment-literal` - section of the text that contains a special value, for example '0-1000 kg' * `segment-bold` - section of the text that should be rendered as bold * `segment-italic` - section of the text that should be rendered as italic * `tooltip` - used on paragraphs that should be rendered with a tooltip. A section of the text in such a paragraph will have a class `tooltip-text` and is intended to be shown in a mouse over dialog. If the style is not used, the `tooltip-text` section would be shown on a new line, after the main part of the text. * `tooltip-text` - marks a section of the text within a `tooltip`, that is intended to be shown in a mouse over dialog. * `tooltip-icon` - marks a section of the text within a `tooltip`, that can be replaced with a tooltip icon, for example '?' or 'i'. By default, this section contains a `br` tag, that is separating the main text and the tooltip text when the style is not used. * `tooltip-style-question` - the tooltip shows helpful information, can use the '?' as an icon. * `tooltip-style-info` - the tooltip adds additional information fitting to the context, can use the 'i' as an icon.", "type": "string"}, "title": {"description": "Title of the issue.", "type": "string"}}, "type": "object"}, "Stats": {"description": "Products statistics.", "id": "Stats", "properties": {"activeCount": {"description": "The number of products that are active.", "format": "int64", "type": "string"}, "disapprovedCount": {"description": "The number of products that are disapproved.", "format": "int64", "type": "string"}, "expiringCount": {"description": "The number of products that are expiring.", "format": "int64", "type": "string"}, "pendingCount": {"description": "The number of products that are pending.", "format": "int64", "type": "string"}}, "type": "object"}, "TextInput": {"description": "Text input allows the business to provide a text value.", "id": "TextInput", "properties": {"additionalInfo": {"$ref": "TextWithTooltip", "description": "Additional info regarding the field to be displayed to the business. For example, warning to not include personal identifiable information. There may be more information to be shown in a tooltip."}, "ariaLabel": {"description": "Text to be used as the [aria-label](https://www.w3.org/TR/WCAG20-TECHS/ARIA14.html) for the input.", "type": "string"}, "formatInfo": {"description": "Information about the required format. If present, it should be shown close to the input field to help the business to provide a correct value. For example: \"VAT numbers should be in a format similar to SK9999999999\"", "type": "string"}, "type": {"description": "Type of the text input", "enum": ["TEXT_INPUT_TYPE_UNSPECIFIED", "GENERIC_SHORT_TEXT", "GENERIC_LONG_TEXT"], "enumDescriptions": ["Default value. Will never be provided by the API.", "Used when a short text is expected. The field can be rendered as a [text field](https://www.w3.org/TR/2012/WD-html-markup-********/input.text.html#input.text).", "Used when a longer text is expected. The field should be rendered as a [textarea](https://www.w3.org/TR/2012/WD-html-markup-********/textarea.html#textarea)."], "type": "string"}}, "type": "object"}, "TextInputValue": {"description": "Value for text input field.", "id": "TextInputValue", "properties": {"value": {"description": "Required. Text provided by the business.", "type": "string"}}, "type": "object"}, "TextWithTooltip": {"description": "Block of text that may contain a tooltip with more information.", "id": "TextWithTooltip", "properties": {"simpleTooltipValue": {"description": "Value of the tooltip as a simple text.", "type": "string"}, "simpleValue": {"description": "Value of the message as a simple text.", "type": "string"}, "tooltipIconStyle": {"description": "The suggested type of an icon for tooltip, if a tooltip is present.", "enum": ["TOOLTIP_ICON_STYLE_UNSPECIFIED", "INFO", "QUESTION"], "enumDescriptions": ["Default value. Will never be provided by the API.", "Used when the tooltip adds additional information to the context, the 'i' can be used as an icon.", "Used when the tooltip shows helpful information, the '?' can be used as an icon."], "type": "string"}}, "type": "object"}, "TriggerActionPayload": {"description": "The payload for the triggered action.", "id": "TriggerActionPayload", "properties": {"actionContext": {"description": "Required. The context from the selected action. The value is obtained from rendered issues and needs to be sent back to identify the action that is being triggered.", "type": "string"}, "actionInput": {"$ref": "ActionInput", "description": "Required. Input provided by the business."}}, "type": "object"}, "TriggerActionResponse": {"description": "Response informing about the started action.", "id": "TriggerActionResponse", "properties": {"message": {"description": "The message for the business.", "type": "string"}}, "type": "object"}}, "servicePath": "", "title": "Merchant API", "version": "issueresolution_v1beta", "version_module": true}