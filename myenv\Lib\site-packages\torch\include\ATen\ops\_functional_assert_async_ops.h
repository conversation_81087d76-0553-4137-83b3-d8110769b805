#pragma once

// @generated by torchgen/gen.py from Operator.h

#include <string_view>
#include <tuple>
#include <vector>

// Forward declarations of any types needed in the operator signatures.
// We can't directly include these classes because it will cause circular include dependencies.
// This file is included by TensorBody.h, which defines the Tensor class.
#include <ATen/core/ATen_fwd.h>

namespace at {
namespace _ops {


struct TORCH_API _functional_assert_async_msg {
  using schema = at::Tensor (const at::Tensor &, c10::string_view, const at::Tensor &);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::_functional_assert_async";
  static constexpr const char* overload_name = "msg";
  static constexpr const char* schema_str = "_functional_assert_async.msg(Tensor self, str assert_msg, Tensor dep_token) -> Tensor";
  static at::Tensor call(const at::Tensor & self, c10::string_view assert_msg, const at::Tensor & dep_token);
  static at::Tensor redispatch(c10::DispatchKeySet dispatchKeySet, const at::Tensor & self, c10::string_view assert_msg, const at::Tensor & dep_token);
};

}} // namespace at::_ops
